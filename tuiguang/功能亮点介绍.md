# Edgemind 功能与亮点

## 核心优势

*   **一键式安装，开箱即用**
    *   **亮点描述**：通过集成化的安装脚本，将Docker、Ollama大模型环境等复杂依赖一并配置妥当。用户无需具备深厚的IT知识，也无需担心网络限制，即可在个人电脑或服务器上快速部署一套功能完备的本地AI服务。
    *   **应用场景**：
        1.  **中小企业**：希望在团队内部署AI助手，但缺乏专业运维人员，可实现快速落地。
        2.  **开发者与爱好者**：希望在个人电脑上探索大模型能力，可免去繁琐的环境配置，专注于应用和开发。
        3.  **教育机构**：为学生快速搭建AI实验环境，用于教学和课程设计。
        4.  **非技术人员**：作家、研究员等希望拥有个人AI助手，但不想处理技术难题。
        5.  **临时项目**：为黑客松、短期项目或产品演示快速部署功能齐全的AI后台。

*   **完全离线，数据私有**
    *   **亮点描述**：系统所有组件，从大语言模型到向量数据库，均可在本地环境运行，实现与公网的物理隔离。所有文档、对话记录和用户数据都安全地存储在您自己的设备上，确保了最高级别的数据安全与隐私。
    *   **应用场景**：
        1.  **敏感行业**：法务、研发、财务、咨询等处理高度敏感商业机密的组织。
        2.  **医疗领域**：在不违反隐私法规的前提下，分析和处理患者数据。
        3.  **政府与公共部门**：处理机密或内部文件，确保信息安全可控。
        4.  **新闻从业者**：保护匿名信源和敏感的调查资料。
        5.  **个人隐私**：管理个人日记、财务记录、家庭档案等私密信息。

*   **浏览器访问，跨平台使用**
    *   **亮点描述**：采用先进的B/S架构，用户只需通过浏览器即可访问AI助手全部功能。无需在每台电脑上安装和更新客户端软件，实现真正的跨平台（Windows, macOS, Linux）无差别体验。
    *   **应用场景**：
        1.  **混合办公**：员工在办公室、家里或出差途中，都能通过浏览器无缝访问。
        2.  **多设备用户**：在台式机、笔记本、平板电脑之间轻松切换，保持工作连续性。
        3.  **大型组织**：IT部门只需维护服务端，即可向全公司的数千名员工提供服务，极大简化软件分发和管理。
        4.  **跨系统协作**：设计团队用macOS，开发团队用Linux，管理团队用Windows，所有人都能在同一个平台上协作。
        5.  **访客访问**：为临时顾问或合作伙伴提供受限的Web访问权限，而无需对方安装任何软件。

## 智能对话与知识管理

*   **文档树对话，精准定位**
    *   **亮点描述**：颠覆传统“一锅烩”的知识库问答模式，将您的文件系统映射为一棵可交互的“知识树”。您可以针对任何一个文件或文件夹发起独立的、有上下文的对话，让AI的回答范围被精确限定，从而获得最相关的答案。
    *   **应用场景**：
        1.  **项目管理**：进入“2024年Q3营销活动”文件夹提问，AI的回答将只基于该活动的相关文档，避免信息干扰。
        2.  **新员工入职**：让新员工与“入职培训”文件夹对话，快速了解公司制度和岗位职责。
        3.  **法律咨询**：律师可针对特定案件的文件夹进行提问，快速在证据和卷宗中定位关键信息。
        4.  **学术研究**：针对存放特定课题论文的文件夹进行对话，深入挖掘和梳理该领域的知识脉络。
        5.  **产品比对**：分别在“A产品”和“B产品”的资料文件夹中提问相同问题，快速获得并列比对信息。

*   **深度思考**
    *   **亮点描述**：内置强大的AI智能体（Agent），不仅能回答事实性问题，更能执行复杂的、多步骤的思考任务。它能够理解您的深层意图，跨文档整合信息，进行归纳、分析、推理，并最终生成富有洞察力的报告或建议。
    *   **应用场景**：
        1.  **市场分析**：上传竞品分析报告、用户访谈记录和销售数据，让AI生成一份“市场机会分析报告”。
        2.  **会议纪要**：上传会议录音转录稿，让AI自动提炼会议核心要点、任务分配（TODO List）和后续跟进事项。
        3.  **创意写作**：提供一个故事大纲和几篇背景资料，让AI创作一篇小说或剧本的初稿。
        4.  **代码辅助**：上传一个项目的多个代码文件，让AI“阅读”后，为新功能提供实现思路或重构建议。
        5.  **旅行规划**：将多篇旅行攻略、酒店预订单和地图文件放在一个文件夹，让AI“规划一个五日游的详细行程”。

*   **自研 RAG 引擎，高精度检索**
    *   **亮点描述**：搭载了为中文环境深度优化的自研检索增强生成（RAG）引擎。通过智能的文档切分、高效的向量化技术，并结合Milvus/Chroma等多种业界领先的向量数据库，确保在海量知识库中也能实现闪电般快速且高度精准的内容检索。
    *   **应用场景**：
        1.  **企业知识库**：当员工问“去年关于XX产品的报销政策是怎样的？”，系统能从数千份文件中精确定位并给出答案。
        2.  **技术支持**：客服人员输入用户遇到的问题，系统能快速从技术手册和过往案例中找到最可能的解决方案。
        3.  **金融投研**：分析师输入一个宏观经济事件，系统能快速从海量研报和公告中检索出可能受影响的公司和行业。
        4.  **教育辅导**：学生输入一个知识点难题，系统能从教材、课件和习题库中找到相关的定义、例题和解题思路。
        5.  **医疗诊断辅助**：医生输入患者的症状，系统能从医学文献和病例库中检索出相关的疾病信息和诊疗方案作为参考。

## 开放与兼容

*   **大模型中心，随心切换**
    *   **亮点描述**：提供一个可视化的“大模型中心”，用户可以像逛应用商店一样，轻松浏览、下载、管理和切换不同规模和特性的开源大模型。当前已支持包括：
        *   **通义千问系列 (Qwen)**: Qwen3-0.6B, 1.7B, 4B, 8B, 14B, 30B
        *   **Llama 系列 (Meta)**: Llama3.1-8B, 70B; Llama3.2-1B, 3B
        *   **DeepSeek 系列**: DeepSeek-R1-1.5B, 7B, 8B, 14B, 32B
        *   **Gemma 系列 (Google)**: Gemma3-1B, 4B, 12B, 27B
        *   **Phi 系列 (Microsoft)**: Phi-4-14B
        *   **嵌入模型**: BGE-Large (用于知识库检索)
    *   **应用场景**：
        1.  **任务匹配**：使用DeepSeek进行代码生成，切换到Qwen进行中文长文写作，选择最适合任务的模型。
        2.  **成本控制**：在开发和测试阶段使用轻量级模型（如Qwen3-1.7B），在生产环境切换到高性能模型（如Llama3.1-8B）。
        3.  **学术研究**：方便研究人员在同一平台下，对比不同模型在特定数据集上的表现。
        4.  **硬件适应**：根据服务器或个人电脑的显存大小，选择能流畅运行的最佳模型。
        5.  **能力探索**：轻松体验和切换支持多模态（如Gemma3）或深度思考（如Qwen3）等不同特性的模型。

*   **Office 文档深度集成与智能生成**
    *   **亮点描述**：系统深度集成 OnlyOffice，提供强大的 Office 文档**在线预览**功能，支持 Word, Excel, PowerPoint 等多种主流格式。用户无需安装任何本地软件，即可在浏览器中直接、高清地查看文档内容。同时，AI 助手具备智能的 Word 文档生成能力，可根据用户指令自动化创建周报、纪要等结构化文档，生成后可立即在线预览。
    *   **应用场景**：
        1.  **AI 智能报告生成**：用户通过与 AI 对话，快速生成一份结构完整的 Word 格式周报或会议纪要，生成后可立即在浏览器中预览其最终样式，确认无误后下载使用。
        2.  **无缝文档审阅**：团队成员将 Word、Excel 或 PPT 文档上传至知识库后，其他成员无需下载或安装任何 Office 软件，直接点击即可在线打开并审阅内容，极大简化了文件传阅流程。
        3.  **统一文档中心**：将项目的所有相关文档（.docx, .xlsx, .pptx）集中存储在系统中，形成一个统一的文档中心，方便团队成员随时通过浏览器快速查阅，确保信息同步。
        4.  **会议材料准备辅助**：AI 可根据会议议程生成初步的 Word 版会议纪要框架，方便用户下载后快速填充内容。所有会议材料均可上传至系统，供参会者在线预览。
        5.  **工作汇报辅助**：在撰写工作汇报时，可让 AI 生成所需的文本内容，并直接输出为 Word 格式，方便用户下载后进行二次编辑或直接提交。

*   **多格式文件解析**
    *   **亮点描述**：强大的文档解析能力，不仅支持Word、PPT、Excel、TXT等常见格式，更能通过OCR技术自动识别和提取PDF扫描件和图片中的文字内容，让非结构化数据也能被轻松纳入知识库。
    *   **应用场景**：
        1.  **历史档案数字化**：将公司多年的纸质文件、合同扫描成PDF后，一键导入系统，使其变为可搜索的电子知识。
        2.  **会议纪要整理**：将会议室白板的照片直接上传，系统自动提取手写的要点，并整理成文本纪要。
        3.  **票据信息提取**：上传发票、收据的照片，系统自动识别金额、日期、抬头等关键信息。
        4.  **书籍资料电子化**：拍摄书籍或杂志内页，系统能将图片中的文字转化为可复制、可引用的文本内容。
        5.  **图文报告分析**：上传包含大量图表和文字的PDF行业报告，系统能同时提取文本和图片内容，供AI进行综合分析。

## 企业级特性

*   **一键同步**
    *   **亮点描述**：提供便捷的**手动同步**功能。您可以将本地或网络上的文件夹与知识库进行关联，当文件发生变化后，通过在界面上点击“一键同步”按钮，即可快速将新增、修改或删除的文件变更更新到AI的知识库中，确保知识的准确性。
    *   **应用场景**：
        1.  **定期知识更新**：管理员定期（如每天下班前）对团队共享盘执行“一键同步”，确保AI第二天能基于最新文档提供服务。
        2.  **个人知识库管理**：在本地用Obsidian或Typora整理完一批笔记后，手动同步一次文件夹，将新知识注入AI。
        3.  **项目阶段性归档**：项目里程碑达成后，手动同步一次项目文档目录，将阶段性成果固化到知识库中，供AI查询和总结。
        4.  **批量导入新资料**：下载了一批新的行业报告后，将其放入指定文件夹，然后执行一键同步，快速完成批量入库。
        5.  **数据迁移与初始化**：在系统初次部署时，通过一键同步功能，将企业现有的海量存量文档快速、完整地导入知识库。

*   **文档系统分离**
    *   **亮点描述**：采用灵活的架构设计，将应用本身与文档存储系统解耦。这意味着您可以将文档存储在本地磁盘、企业内部的NAS网络存储，甚至是对象存储上，系统都能轻松接入和管理。
    *   **应用场景**：
        1.  **利旧原则**：企业可继续使用现有的NAS或文件服务器，无需为AI系统采购额外的存储设备。
        2.  **云上部署**：将Edgemind部署在云服务器上，同时将其连接到成本更低的云对象存储（如S3、OSS）来存放海量文档。
        3.  **灵活扩容**：当文档数据量激增时，只需独立升级存储系统，应用服务不受影响，扩展性强。
        4.  **数据分层**：可将热点数据存在高性能本地磁盘，冷数据归档到低成本存储，系统可统一访问。
        5.  **备份与容灾**：可独立于应用，对文档存储系统进行专业的备份和容灾管理，保障知识资产安全。