/*
 Navicat Premium Data Transfer

 Source Server         : local-mysql8
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : wkg0502

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 11/05/2025 23:45:01
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for chat_conversation
-- ----------------------------
DROP TABLE IF EXISTS `chat_conversation`;
CREATE TABLE `chat_conversation`  (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会话ID',
                                      `user_id` bigint NOT NULL COMMENT '用户ID',
                                      `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话标题',
                                      `create_time` datetime NOT NULL COMMENT '创建时间',
                                      `update_time` datetime NOT NULL COMMENT '最后更新时间',
                                      `status` tinyint NULL DEFAULT 1 COMMENT '状态 1-正常 0-已删除',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      INDEX `idx_user_id`(`user_id` ASC) USING BTREE COMMENT '用户ID索引',
                                      INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
                                      CONSTRAINT `chat_conversation_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 138 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天会话表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_conversation
-- ----------------------------

-- ----------------------------
-- Table structure for chat_conversation_tag
-- ----------------------------
DROP TABLE IF EXISTS `chat_conversation_tag`;
CREATE TABLE `chat_conversation_tag`  (
                                          `conversation_id` bigint NOT NULL COMMENT '会话ID',
                                          `tag_id` bigint NOT NULL COMMENT '标签ID',
                                          PRIMARY KEY (`conversation_id`, `tag_id`) USING BTREE COMMENT '复合主键',
                                          INDEX `tag_id`(`tag_id` ASC) USING BTREE,
                                          CONSTRAINT `chat_conversation_tag_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `chat_conversation` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
                                          CONSTRAINT `chat_conversation_tag_ibfk_2` FOREIGN KEY (`tag_id`) REFERENCES `chat_tag` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '会话标签关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_conversation_tag
-- ----------------------------

-- ----------------------------
-- Table structure for chat_message
-- ----------------------------
DROP TABLE IF EXISTS `chat_message`;
CREATE TABLE `chat_message`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
                                 `conversation_id` bigint NOT NULL COMMENT '会话ID',
                                 `sender` enum('user','ai') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发送者类型',
                                 `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
                                 `thinking_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'AI思考过程内容',
                                 `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片路径(如果有)',
                                 `token_count` int NULL DEFAULT 0 COMMENT '消息Token数量',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `model_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '使用的模型名称',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 INDEX `idx_conversation_id`(`conversation_id` ASC) USING BTREE COMMENT '会话ID索引',
                                 INDEX `idx_create_time`(`create_time` ASC) USING BTREE COMMENT '创建时间索引',
                                 CONSTRAINT `chat_message_ibfk_1` FOREIGN KEY (`conversation_id`) REFERENCES `chat_conversation` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 796 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_message
-- ----------------------------

-- ----------------------------
-- Table structure for chat_tag
-- ----------------------------
DROP TABLE IF EXISTS `chat_tag`;
CREATE TABLE `chat_tag`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
                             `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
                             `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '#1a73e8' COMMENT '标签颜色',
                             `create_time` datetime NOT NULL COMMENT '创建时间',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `uk_tag_name`(`name` ASC) USING BTREE COMMENT '标签名唯一'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chat_tag
-- ----------------------------

-- ----------------------------
-- Table structure for kb_document_chunks
-- ----------------------------
DROP TABLE IF EXISTS `kb_document_chunks`;
CREATE TABLE `kb_document_chunks`  (
                                       `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                       `document_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的文档ID',
                                       `node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的节点ID',
                                       `chunk_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '向量存储中的分片ID',
                                       `chunk_index` int NOT NULL COMMENT '分片索引号',
                                       `chunk_size` int NOT NULL COMMENT '分片文本长度',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       INDEX `idx_document_chunks_doc_id`(`document_id` ASC) USING BTREE,
                                       INDEX `idx_document_chunks_node_id`(`node_id` ASC) USING BTREE,
                                       INDEX `idx_document_chunks_chunk_id`(`chunk_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9751 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文档分片映射表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_document_chunks
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_documents
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_documents`;
CREATE TABLE `kb_knowledge_documents`  (
                                           `document_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档ID (UUID)',
                                           `node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联的节点 ID',
                                           `mime_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件MIME类型',
                                           `file_size_bytes` bigint NULL DEFAULT NULL COMMENT '文件大小（字节）',
                                           `vector_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'PENDING' COMMENT '向量化状态: PENDING, PROCESSING, INDEXED, FAILED',
                                           `vector_error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '记录向量化失败的原因',
                                           `last_indexed_time` datetime NULL DEFAULT NULL COMMENT '最后成功索引的时间',
                                           `content_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件内容的哈希值',
                                           `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`document_id`) USING BTREE,
                                           UNIQUE INDEX `uq_kb_docs_node_id`(`node_id` ASC) USING BTREE COMMENT '一个节点关联一个文档',
                                           INDEX `idx_kb_docs_vector_status`(`vector_status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文档内容引用和向量化状态表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_documents
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_node_closure
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_node_closure`;
CREATE TABLE `kb_knowledge_node_closure`  (
                                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                              `ancestor_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '祖先节点ID',
                                              `descendant_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '后代节点ID',
                                              `depth` int NOT NULL COMMENT '层级深度 (0表示自身)',
                                              `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE INDEX `uq_kb_node_closure_rel`(`ancestor_id` ASC, `descendant_id` ASC) USING BTREE,
                                              INDEX `idx_kb_node_closure_ancestor`(`ancestor_id` ASC) USING BTREE,
                                              INDEX `idx_kb_node_closure_descendant`(`descendant_id` ASC) USING BTREE,
                                              INDEX `idx_kb_node_closure_depth`(`depth` ASC) USING BTREE,
                                              CONSTRAINT `fk_kb_node_closure_ancestor` FOREIGN KEY (`ancestor_id`) REFERENCES `kb_knowledge_nodes` (`node_id`) ON DELETE CASCADE ON UPDATE CASCADE,
                                              CONSTRAINT `fk_kb_node_closure_descendant` FOREIGN KEY (`descendant_id`) REFERENCES `kb_knowledge_nodes` (`node_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 447 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库节点闭包表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_node_closure
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_nodes
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_nodes`;
CREATE TABLE `kb_knowledge_nodes`  (
                                       `node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点ID (UUID)',
                                       `space_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属知识空间 ID',
                                       `parent_node_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父节点 ID，根节点为 NULL',
                                       `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点名称',
                                       `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '节点类型: FOLDER, FILE',
                                       `create_by` bigint NULL DEFAULT NULL COMMENT '创建者用户ID (sys_user.id)',
                                       `update_by` bigint NULL DEFAULT NULL COMMENT '最后更新者用户ID (sys_user.id)',
                                       `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                       PRIMARY KEY (`node_id`) USING BTREE,
                                       UNIQUE INDEX `uq_kb_nodes_parent_name`(`space_id` ASC, `parent_node_id` ASC, `name` ASC) USING BTREE COMMENT '同一父节点下名称唯一 (限制 parent_node_id 索引长度避免超长)',
                                       INDEX `idx_kb_nodes_space_id`(`space_id` ASC) USING BTREE,
                                       INDEX `idx_kb_nodes_parent_id`(`parent_node_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识库节点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_nodes
-- ----------------------------

-- ----------------------------
-- Table structure for kb_knowledge_spaces
-- ----------------------------
DROP TABLE IF EXISTS `kb_knowledge_spaces`;
CREATE TABLE `kb_knowledge_spaces`  (
                                        `space_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识空间ID (UUID)',
                                        `owner_user_id` bigint NULL DEFAULT NULL COMMENT '私人空间的所有者 ID (sys_user.id)，团队空间时为 NULL',
                                        `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '空间名称',
                                        `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '空间描述',
                                        `is_private` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为私人空间 (1=私人, 0=团队)',
                                        `create_by` bigint NULL DEFAULT NULL COMMENT '创建者用户ID (sys_user.id)',
                                        `update_by` bigint NULL DEFAULT NULL COMMENT '最后更新者用户ID (sys_user.id)',
                                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`space_id`) USING BTREE,
                                        UNIQUE INDEX `uq_kb_spaces_owner`(`owner_user_id` ASC) USING BTREE COMMENT '确保每个用户最多一个私人空间',
                                        INDEX `idx_kb_spaces_owner`(`owner_user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '知识空间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kb_knowledge_spaces
-- ----------------------------
INSERT INTO `kb_knowledge_spaces` VALUES ('956ed750-9629-4734-97af-6f8903ab8564', 1, '个人库', '个人私有知识空间', 1, 1, 1, '2025-05-02 16:35:38', '2025-05-02 16:35:38');
INSERT INTO `kb_knowledge_spaces` VALUES ('9b57d802-c4e5-40be-831e-d394f870baa6', NULL, '知识库', '公共团队知识空间', 0, 1, 1, '2025-05-02 16:32:08', '2025-05-02 16:32:08');

-- ----------------------------
-- Table structure for sys_department
-- ----------------------------
DROP TABLE IF EXISTS `sys_department`;
CREATE TABLE `sys_department`  (
                                   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '部门ID',
                                   `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '部门名称',
                                   `dept_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门编码',
                                   `parent_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '父部门ID (0表示顶级)',
                                   `dept_level` int UNSIGNED NULL DEFAULT 1 COMMENT '部门层级',
                                   `dept_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门路径(用于快速查询)',
                                   `manager_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '部门负责人ID',
                                   `sort_order` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '显示顺序',
                                   `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 (0: 停用, 1: 正常)',
                                   `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门描述/职责',
                                   `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门联系电话',
                                   `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门联系邮箱',
                                   `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门地址',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_dept_code`(`dept_code` ASC) USING BTREE,
                                   INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
                                   INDEX `idx_status`(`status` ASC) USING BTREE,
                                   INDEX `idx_manager_id`(`manager_id` ASC) USING BTREE,
                                   INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '部门结构表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_department
-- ----------------------------
INSERT INTO `sys_department` (`id`, `dept_name`, `dept_code`, `parent_id`, `dept_level`, `dept_path`, `manager_id`, `sort_order`, `status`, `description`, `contact_phone`, `contact_email`, `address`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, '总部', NULL, 0, 1, '/1', NULL, 0, 1, NULL, NULL, NULL, NULL, '2025-04-26 12:56:11', '2025-04-26 12:56:11', NULL, NULL, 0);

-- ----------------------------
-- Table structure for sys_license
-- ----------------------------
DROP TABLE IF EXISTS `sys_license`;
CREATE TABLE `sys_license`  (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '许可证ID',
                                `license_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '许可证密钥',
                                `hardware_fingerprint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '硬件指纹',
                                `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '激活状态：0-未激活，1-已激活',
                                `activated_time` datetime NULL DEFAULT NULL COMMENT '激活时间',
                                `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间，NULL表示永不过期',
                                `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统许可证表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_license
-- ----------------------------

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
                                   `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '权限ID',
                                   `permission_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
                                   `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限编码 (用于权限控制)',
                                   `type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限类型 (MENU, BUTTON, API, DATA)',
                                   `parent_id` bigint UNSIGNED NULL DEFAULT 0 COMMENT '父权限ID (0表示顶级)',
                                   `resource_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源URL',
                                   `http_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'HTTP方法',
                                   `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
                                   `sort_order` int DEFAULT '0' COMMENT '排序',
                                   `is_external` tinyint(1) DEFAULT '0' COMMENT '是否外部链接',
                                   `component_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
                                   `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权限描述',
                                   `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 (0: 禁用, 1: 启用)',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                   `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                   `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_permission_code`(`permission_code` ASC) USING BTREE,
                                   INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
                                   INDEX `idx_type`(`type` ASC) USING BTREE,
                                   INDEX `idx_status`(`status` ASC) USING BTREE,
                                   INDEX `idx_permission_code`(`permission_code` ASC) USING BTREE,
                                   INDEX `idx_type_status`(`type`, `status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '权限信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_permission
-- ----------------------------
-- 菜单权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, 'AI对话', 'menu:view:ai_chat', 'MENU', 0, NULL, NULL, 'bi-chat-left-text', 1, 0, NULL, 'AI对话菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (2, 'AI知识库', 'menu:view:ai_knowledge', 'MENU', 0, NULL, NULL, 'bi-people', 2, 0, NULL, 'AI知识库菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (3, '模型中心', 'menu:view:models', 'MENU', 0, NULL, NULL, 'bi-cpu', 3, 0, NULL, '模型中心菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (4, '系统设置', 'menu:view:settings', 'MENU', 0, NULL, NULL, 'bi-gear', 4, 0, NULL, '系统设置菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (5, '权限管理', 'menu:view:permission_group', 'MENU', 0, NULL, NULL, 'bi-shield-lock', 5, 0, NULL, '权限管理菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (6, '用户管理', 'menu:view:users', 'MENU', 5, NULL, NULL, 'bi-person', 1, 0, NULL, '用户管理子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (7, '部门管理', 'menu:view:departments', 'MENU', 5, NULL, NULL, 'bi-diagram-3', 2, 0, NULL, '部门管理子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (8, '角色管理', 'menu:view:roles', 'MENU', 5, NULL, NULL, 'bi-people', 3, 0, NULL, '角色管理子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (9, '权限设置', 'menu:view:permissions', 'MENU', 5, NULL, NULL, 'bi-key', 4, 0, NULL, '权限设置子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (10, '操作日志', 'menu:view:logs', 'MENU', 5, NULL, NULL, 'bi-journal-text', 5, 0, NULL, '操作日志子菜单', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
-- 用户管理API权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (11, '查询用户列表', 'user:manage:list', 'API', 6, '/api/system/user/list', 'GET', NULL, 1, 0, NULL, '查询用户列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (12, '创建用户', 'user:manage:create', 'API', 6, '/api/system/user', 'POST', NULL, 2, 0, NULL, '创建用户权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (13, '更新用户', 'user:manage:update', 'API', 6, '/api/system/user/*', 'PUT', NULL, 3, 0, NULL, '更新用户权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (14, '删除用户', 'user:manage:delete', 'API', 6, '/api/system/user/*', 'DELETE', NULL, 4, 0, NULL, '删除用户权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (15, '分配用户角色', 'user:manage:assign-role', 'API', 6, '/api/system/user/*/roles', 'POST', NULL, 5, 0, NULL, '分配用户角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (16, '重置用户密码', 'user:manage:reset-password', 'API', 6, '/api/system/user/*/reset-password', 'POST', NULL, 6, 0, NULL, '重置用户密码权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (17, '导出用户数据', 'user:manage:export', 'API', 6, '/api/system/user/export', 'POST', NULL, 7, 0, NULL, '导出用户数据权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
-- 角色管理API权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (18, '查询角色列表', 'role:manage:list', 'API', 8, '/api/system/role/list', 'GET', NULL, 1, 0, NULL, '查询角色列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (19, '创建角色', 'role:manage:create', 'API', 8, '/api/system/role', 'POST', NULL, 2, 0, NULL, '创建角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (20, '更新角色', 'role:manage:update', 'API', 8, '/api/system/role/*', 'PUT', NULL, 3, 0, NULL, '更新角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (21, '删除角色', 'role:manage:delete', 'API', 8, '/api/system/role/*', 'DELETE', NULL, 4, 0, NULL, '删除角色权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (22, '分配角色权限', 'role:manage:assign-permission', 'API', 8, '/api/system/role/*/permissions', 'POST', NULL, 5, 0, NULL, '分配角色权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
-- 权限管理API权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (23, '查询权限列表', 'permission:manage:list', 'API', 9, '/api/system/permission/list', 'GET', NULL, 1, 0, NULL, '查询权限列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (24, '创建权限', 'permission:manage:create', 'API', 9, '/api/system/permission', 'POST', NULL, 2, 0, NULL, '创建权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (25, '更新权限', 'permission:manage:update', 'API', 9, '/api/system/permission/*', 'PUT', NULL, 3, 0, NULL, '更新权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (26, '删除权限', 'permission:manage:delete', 'API', 9, '/api/system/permission/*', 'DELETE', NULL, 4, 0, NULL, '删除权限权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

-- 部门管理API权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (27, '查询部门列表', 'dept:manage:list', 'API', 7, '/api/system/dept/tree', 'GET', NULL, 1, 0, NULL, '查询部门列表权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (28, '创建部门', 'dept:manage:create', 'API', 7, '/api/system/dept', 'POST', NULL, 2, 0, NULL, '创建部门权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (29, '更新部门', 'dept:manage:update', 'API', 7, '/api/system/dept/*', 'PUT', NULL, 3, 0, NULL, '更新部门权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (30, '删除部门', 'dept:manage:delete', 'API', 7, '/api/system/dept/*', 'DELETE', NULL, 4, 0, NULL, '删除部门权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

-- 操作日志API权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (31, '查询操作日志', 'log:manage:list', 'API', 10, '/api/system/log/list', 'GET', NULL, 1, 0, NULL, '查询操作日志权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (32, '导出操作日志', 'log:manage:export', 'API', 10, '/api/system/log/export', 'POST', NULL, 2, 0, NULL, '导出操作日志权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (33, '清理操作日志', 'log:manage:delete', 'API', 10, '/api/system/log/clean', 'POST', NULL, 3, 0, NULL, '清理操作日志权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

-- 系统配置API权限
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (34, '查询系统配置', 'system:config:list', 'API', 4, '/api/system/config/list', 'GET', NULL, 1, 0, NULL, '查询系统配置权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);
INSERT INTO `sys_permission` (`id`, `permission_name`, `permission_code`, `type`, `parent_id`, `resource_url`, `http_method`, `icon`, `sort_order`, `is_external`, `component_path`, `description`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (35, '更新系统配置', 'system:config:update', 'API', 4, '/api/system/config/*', 'PUT', NULL, 2, 0, NULL, '更新系统配置权限', 1, '2025-04-26 12:58:26', '2025-04-26 12:58:26', NULL, NULL, 0);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
                             `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '角色ID',
                             `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
                             `role_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色编码 (用于权限控制)',
                             `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色描述',
                             `data_scope` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'DEPT' COMMENT '数据权限范围',
                             `dept_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '数据权限部门ID列表',
                             `sort_order` int DEFAULT '0' COMMENT '排序',
                             `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 (0: 禁用, 1: 启用)',
                             `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `create_by` bigint DEFAULT NULL COMMENT '创建人',
                             `update_by` bigint DEFAULT NULL COMMENT '更新人',
                             `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `uk_role_code`(`role_code` ASC) USING BTREE,
                             INDEX `idx_status`(`status` ASC) USING BTREE,
                             INDEX `idx_role_code`(`role_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `data_scope`, `dept_ids`, `sort_order`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, '超级管理员', 'admin', '拥有系统所有权限', 'ALL', NULL, 0, 1, '2025-04-26 12:56:30', '2025-04-26 12:56:30', NULL, NULL, 0);
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`, `data_scope`, `dept_ids`, `sort_order`, `status`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (2, '普通用户', 'user', '拥有基本访问和操作权限', 'DEPT', NULL, 1, 1, '2025-04-26 12:56:30', '2025-04-26 12:56:30', NULL, NULL, 0);

-- ----------------------------
-- Table structure for sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`  (
                                        `role_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
                                        `permission_id` bigint UNSIGNED NOT NULL COMMENT '权限ID',
                                        PRIMARY KEY (`role_id`, `permission_id`) USING BTREE,
                                        INDEX `idx_rp_permission_id`(`permission_id` ASC) USING BTREE,
                                        INDEX `idx_rp_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_permission
-- ----------------------------
-- 超级管理员角色(ID=1)拥有所有权限
INSERT INTO `sys_role_permission` VALUES (1, 1);   -- AI对话菜单
INSERT INTO `sys_role_permission` VALUES (1, 2);   -- AI知识库菜单
INSERT INTO `sys_role_permission` VALUES (1, 3);   -- 模型中心菜单
INSERT INTO `sys_role_permission` VALUES (1, 4);   -- 系统设置菜单
INSERT INTO `sys_role_permission` VALUES (1, 5);   -- 权限管理菜单
INSERT INTO `sys_role_permission` VALUES (1, 6);   -- 用户管理子菜单
INSERT INTO `sys_role_permission` VALUES (1, 7);   -- 部门管理子菜单
INSERT INTO `sys_role_permission` VALUES (1, 8);   -- 角色管理子菜单
INSERT INTO `sys_role_permission` VALUES (1, 9);   -- 权限设置子菜单
INSERT INTO `sys_role_permission` VALUES (1, 10);  -- 操作日志子菜单
INSERT INTO `sys_role_permission` VALUES (1, 11);  -- 查询用户列表
INSERT INTO `sys_role_permission` VALUES (1, 12);  -- 创建用户
INSERT INTO `sys_role_permission` VALUES (1, 13);  -- 更新用户
INSERT INTO `sys_role_permission` VALUES (1, 14);  -- 删除用户
INSERT INTO `sys_role_permission` VALUES (1, 15);  -- 分配用户角色
INSERT INTO `sys_role_permission` VALUES (1, 16);  -- 重置用户密码
INSERT INTO `sys_role_permission` VALUES (1, 17);  -- 导出用户数据
INSERT INTO `sys_role_permission` VALUES (1, 18);  -- 查询角色列表
INSERT INTO `sys_role_permission` VALUES (1, 19);  -- 创建角色
INSERT INTO `sys_role_permission` VALUES (1, 20);  -- 更新角色
INSERT INTO `sys_role_permission` VALUES (1, 21);  -- 删除角色
INSERT INTO `sys_role_permission` VALUES (1, 22);  -- 分配角色权限
INSERT INTO `sys_role_permission` VALUES (1, 23);  -- 查询权限列表
INSERT INTO `sys_role_permission` VALUES (1, 24);  -- 创建权限
INSERT INTO `sys_role_permission` VALUES (1, 25);  -- 更新权限
INSERT INTO `sys_role_permission` VALUES (1, 26);  -- 删除权限
INSERT INTO `sys_role_permission` VALUES (1, 27);  -- 查询部门列表
INSERT INTO `sys_role_permission` VALUES (1, 28);  -- 创建部门
INSERT INTO `sys_role_permission` VALUES (1, 29);  -- 更新部门
INSERT INTO `sys_role_permission` VALUES (1, 30);  -- 删除部门
INSERT INTO `sys_role_permission` VALUES (1, 31);  -- 查询操作日志
INSERT INTO `sys_role_permission` VALUES (1, 32);  -- 导出操作日志
INSERT INTO `sys_role_permission` VALUES (1, 33);  -- 清理操作日志
INSERT INTO `sys_role_permission` VALUES (1, 34);  -- 查询系统配置
INSERT INTO `sys_role_permission` VALUES (1, 35);  -- 更新系统配置

-- 普通用户角色(ID=2)只拥有基本菜单权限
INSERT INTO `sys_role_permission` VALUES (2, 1);   -- AI对话菜单
INSERT INTO `sys_role_permission` VALUES (2, 2);   -- AI知识库菜单
INSERT INTO `sys_role_permission` VALUES (2, 3);   -- 模型中心菜单

-- ----------------------------
-- Table structure for sys_settings
-- ----------------------------
DROP TABLE IF EXISTS `sys_settings`;
CREATE TABLE `sys_settings`  (
                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '设置ID',
                                 `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设置键名',
                                 `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设置值',
                                 `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置描述',
                                 `setting_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设置分组',
                                 `setting_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'STRING' COMMENT '设置类型',
                                 `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统内置',
                                 `sort_order` int DEFAULT '0' COMMENT '排序',
                                 `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `create_by` bigint DEFAULT NULL COMMENT '创建人',
                                 `update_by` bigint DEFAULT NULL COMMENT '更新人',
                                 PRIMARY KEY (`id`) USING BTREE,
                                 UNIQUE INDEX `uk_setting_key`(`setting_key` ASC) USING BTREE,
                                 INDEX `idx_setting_group`(`setting_group` ASC) USING BTREE,
                                 INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_settings
-- ----------------------------
INSERT INTO `sys_settings` (`setting_key`, `setting_value`, `description`, `setting_group`, `setting_type`, `is_system`, `sort_order`, `status`) VALUES
('system.name', 'EdgeMind智能知识管理系统', '系统名称', 'SYSTEM', 'STRING', 1, 1, 1),
('system.version', '1.0.0', '系统版本', 'SYSTEM', 'STRING', 1, 2, 1),
('system.logo', '/wkg/images/logo.png', '系统Logo', 'APPEARANCE', 'IMAGE', 1, 1, 1),
('system.favicon', '/wkg/images/favicon.ico', '网站图标', 'APPEARANCE', 'IMAGE', 1, 2, 1),
('security.password.min_length', '6', '密码最小长度', 'SECURITY', 'NUMBER', 1, 1, 1),
('security.password.require_special', 'false', '密码是否需要特殊字符', 'SECURITY', 'BOOLEAN', 1, 2, 1),
('security.login.max_attempts', '5', '登录最大尝试次数', 'SECURITY', 'NUMBER', 1, 3, 1),
('security.session.timeout', '7200', '会话超时时间（秒）', 'SECURITY', 'NUMBER', 1, 4, 1),
('log.operation.retention_days', '90', '操作日志保留天数', 'LOG', 'NUMBER', 1, 1, 1),
('log.operation.auto_cleanup', 'true', '是否自动清理过期日志', 'LOG', 'BOOLEAN', 1, 2, 1),
('notification.email.enabled', 'false', '是否启用邮件通知', 'NOTIFICATION', 'BOOLEAN', 1, 1, 1),
('notification.sms.enabled', 'false', '是否启用短信通知', 'NOTIFICATION', 'BOOLEAN', 1, 2, 1);

-- ----------------------------
-- Table structure for sys_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
    `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作用户名',
    `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作模块',
    `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
    `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作描述',
    `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求方法',
    `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求URL',
    `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数',
    `response_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '响应结果',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
    `error_message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '错误信息',
    `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作IP',
    `user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户代理',
    `execution_time` bigint DEFAULT NULL COMMENT '执行时间（毫秒）',
    `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE,
    KEY `idx_username` (`username`) USING BTREE,
    KEY `idx_module` (`module`) USING BTREE,
    KEY `idx_operation_type` (`operation_type`) USING BTREE,
    KEY `idx_status` (`status`) USING BTREE,
    KEY `idx_operation_time` (`operation_time`) USING BTREE,
    KEY `idx_ip_address` (`ip_address`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作日志表' ROW_FORMAT=Dynamic;

-- ----------------------------
-- Records of sys_operation_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` bigint DEFAULT NULL COMMENT '用户ID',
    `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名',
    `login_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'WEB' COMMENT '登录类型：WEB,MOBILE,API',
    `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录IP',
    `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '登录地点',
    `browser` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '浏览器',
    `os` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作系统',
    `device_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备类型：Desktop,Mobile,Tablet',
    `user_agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户代理',
    `login_status` tinyint NOT NULL COMMENT '登录状态：0-失败，1-成功',
    `failure_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '失败原因',
    `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会话ID',
    `token_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Token值',
    `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `logout_time` datetime DEFAULT NULL COMMENT '登出时间',
    `online_duration` bigint DEFAULT NULL COMMENT '在线时长（分钟）',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_user_id` (`user_id`) USING BTREE,
    KEY `idx_username` (`username`) USING BTREE,
    KEY `idx_login_status` (`login_status`) USING BTREE,
    KEY `idx_login_time` (`login_time`) USING BTREE,
    KEY `idx_ip_address` (`ip_address`) USING BTREE,
    KEY `idx_session_id` (`session_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='登录日志表' ROW_FORMAT=Dynamic;

-- ----------------------------
-- Records of sys_login_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                             `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
                             `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码 (存储加密后的值)',
                             `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户昵称/姓名',
                             `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
                             `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
                             `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
                             `status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '用户状态 (0: 禁用, 1: 启用)',
                             `dept_id` bigint UNSIGNED NULL DEFAULT NULL COMMENT '所属部门ID',
                             `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
                             `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
                             `last_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
                             `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间',
                             `account_locked` tinyint(1) DEFAULT '0' COMMENT '账号是否锁定',
                             `lock_time` datetime DEFAULT NULL COMMENT '锁定时间',
                             `failed_login_attempts` int DEFAULT '0' COMMENT '登录失败次数',
                             `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             `create_by` bigint DEFAULT NULL COMMENT '创建人',
                             `update_by` bigint DEFAULT NULL COMMENT '更新人',
                             `deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
                             PRIMARY KEY (`id`) USING BTREE,
                             UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE,
                             INDEX `idx_username`(`username` ASC) USING BTREE,
                             INDEX `idx_status`(`status` ASC) USING BTREE,
                             INDEX `idx_dept_id`(`dept_id` ASC) USING BTREE,
                             INDEX `idx_email`(`email` ASC) USING BTREE,
                             INDEX `idx_phone`(`phone` ASC) USING BTREE,
                             INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `avatar`, `status`, `dept_id`, `remark`, `last_login_time`, `last_login_ip`, `password_update_time`, `account_locked`, `lock_time`, `failed_login_attempts`, `create_time`, `update_time`, `create_by`, `update_by`, `deleted`)
VALUES (1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', '管理员', '<EMAIL>', '***********', NULL, 1, 1, '初始超级管理员', NULL, NULL, NULL, 0, NULL, 0, '2025-04-26 12:57:48', '2025-04-26 12:57:48', NULL, NULL, 0);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
                                  `user_id` bigint NOT NULL COMMENT '用户ID',
                                  `role_id` bigint UNSIGNED NOT NULL COMMENT '角色ID',
                                  PRIMARY KEY (`user_id`, `role_id`) USING BTREE,
                                  INDEX `idx_ur_role_id`(`role_id` ASC) USING BTREE,
                                  INDEX `idx_ur_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);

-- ----------------------------
-- Table structure for user_preference
-- ----------------------------
DROP TABLE IF EXISTS `user_preference`;
CREATE TABLE `user_preference`  (
                                    `user_id` bigint NOT NULL COMMENT '用户ID',
                                    `preferred_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '偏好的模型',
                                    `theme` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'light' COMMENT '主题偏好',
                                    `display_thinking` tinyint(1) NULL DEFAULT 1 COMMENT '是否显示思考过程',
                                    `last_active_conversation_id` bigint NULL DEFAULT NULL COMMENT '最后活动的会话ID',
                                    `update_time` datetime NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`user_id`) USING BTREE,
                                    INDEX `last_active_conversation_id`(`last_active_conversation_id` ASC) USING BTREE,
                                    CONSTRAINT `user_preference_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
                                    CONSTRAINT `user_preference_ibfk_2` FOREIGN KEY (`last_active_conversation_id`) REFERENCES `chat_conversation` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户偏好设置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_preference
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
