package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 权限 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    /**
     * 根据权限编码查询权限
     * 
     * @param permissionCode 权限编码
     * @return 权限
     */
    @Select("SELECT * FROM sys_permission WHERE permission_code = #{permissionCode} AND status = 1")
    Permission selectByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 根据父权限ID查询子权限列表
     * 
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE parent_id = #{parentId} AND status = 1 ORDER BY sort_order, id")
    List<Permission> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据权限类型查询权限列表
     * 
     * @param type 权限类型
     * @return 权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE type = #{type} AND status = 1 ORDER BY sort_order, id")
    List<Permission> selectByType(@Param("type") String type);

    /**
     * 查询所有启用的权限
     * 
     * @return 权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE status = 1 ORDER BY sort_order, id")
    List<Permission> selectAllEnabled();

    /**
     * 根据角色ID查询权限列表
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Select("SELECT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id = #{roleId} AND p.status = 1 " +
            "ORDER BY p.sort_order, p.id")
    List<Permission> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色ID列表查询权限列表
     * 
     * @param roleIds 角色ID列表
     * @return 权限列表
     */
    @Select("<script>" +
            "SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "WHERE rp.role_id IN " +
            "<foreach collection='roleIds' item='roleId' open='(' separator=',' close=')'>" +
            "#{roleId}" +
            "</foreach>" +
            " AND p.status = 1 ORDER BY p.sort_order, p.id" +
            "</script>")
    List<Permission> selectByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据用户ID查询权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.* FROM sys_permission p " +
            "INNER JOIN sys_role_permission rp ON p.id = rp.permission_id " +
            "INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.status = 1 " +
            "ORDER BY p.sort_order, p.id")
    List<Permission> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询菜单权限树（用于构建导航菜单）
     * 
     * @return 菜单权限列表
     */
    @Select("SELECT * FROM sys_permission WHERE type = 'MENU' AND status = 1 ORDER BY sort_order, id")
    List<Permission> selectMenuPermissions();

    /**
     * 统计权限下的子权限数量
     * 
     * @param parentId 父权限ID
     * @return 子权限数量
     */
    @Select("SELECT COUNT(*) FROM sys_permission WHERE parent_id = #{parentId}")
    Long countChildPermissions(@Param("parentId") Long parentId);

    /**
     * 统计使用该权限的角色数量
     * 
     * @param permissionId 权限ID
     * @return 角色数量
     */
    @Select("SELECT COUNT(*) FROM sys_role_permission WHERE permission_id = #{permissionId}")
    Long countRolesByPermissionId(@Param("permissionId") Long permissionId);
}
