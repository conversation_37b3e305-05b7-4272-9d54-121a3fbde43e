package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.service.PermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 权限管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/permission")
@RequiredArgsConstructor
@Validated
public class PermissionController {

    private final PermissionService permissionService;

    /**
     * 获取权限树
     */
    @GetMapping("/tree")
    @SaCheckPermission("system:permission:list")
    @OperationLog(operationType = OperationType.QUERY, module = "权限管理", description = "查询权限树")
    public ResponseEntity<ApiResponse<List<Permission>>> getPermissionTree() {
        List<Permission> result = permissionService.getPermissionTree();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取所有权限列表
     */
    @GetMapping("/list")
    @SaCheckPermission("system:permission:list")
    @OperationLog(operationType = OperationType.QUERY, module = "权限管理", description = "查询权限列表")
    public ResponseEntity<ApiResponse<List<Permission>>> getAllPermissions() {
        List<Permission> result = permissionService.list();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据ID获取权限详情
     */
    @GetMapping("/{permissionId}")
    @SaCheckPermission("system:permission:list")
    @OperationLog(operationType = OperationType.QUERY, module = "权限管理", description = "查询权限详情")
    public ResponseEntity<ApiResponse<Permission>> getPermissionById(@PathVariable Long permissionId) {
        Permission result = permissionService.getById(permissionId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 创建权限
     */
    @PostMapping
    @SaCheckPermission("system:permission:create")
    @OperationLog(operationType = OperationType.CREATE, module = "权限管理", description = "创建权限")
    public ResponseEntity<ApiResponse<Void>> createPermission(@Valid @RequestBody Permission permission) {
        permissionService.save(permission);
        return ResponseEntity.ok(ApiResponse.success("权限创建成功"));
    }

    /**
     * 更新权限信息
     */
    @PutMapping("/{permissionId}")
    @SaCheckPermission("system:permission:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "权限管理", description = "更新权限信息")
    public ResponseEntity<ApiResponse<Void>> updatePermission(@PathVariable Long permissionId, 
                                                             @Valid @RequestBody Permission permission) {
        permission.setId(permissionId);
        permissionService.updateById(permission);
        return ResponseEntity.ok(ApiResponse.success("权限信息更新成功"));
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{permissionId}")
    @SaCheckPermission("system:permission:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "权限管理", description = "删除权限")
    public ResponseEntity<ApiResponse<Void>> deletePermission(@PathVariable Long permissionId) {
        permissionService.removeById(permissionId);
        return ResponseEntity.ok(ApiResponse.success("权限删除成功"));
    }

    /**
     * 根据角色ID列表获取权限
     */
    @PostMapping("/by-roles")
    @SaCheckPermission("system:permission:list")
    public ResponseEntity<ApiResponse<List<Permission>>> getPermissionsByRoleIds(@RequestBody List<Long> roleIds) {
        List<Permission> result = permissionService.findPermissionsByRoleIds(roleIds);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }
}
