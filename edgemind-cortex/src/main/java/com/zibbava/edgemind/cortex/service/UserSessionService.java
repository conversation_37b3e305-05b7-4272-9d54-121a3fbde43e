package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.UserSession;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户会话服务接口
 * 
 * <AUTHOR>
 */
public interface UserSessionService extends IService<UserSession> {

    /**
     * 创建用户会话
     * 
     * @param userId 用户ID
     * @param token 访问令牌
     * @param sessionId 会话ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param expireTime 过期时间
     * @return 用户会话
     */
    UserSession createSession(Long userId, String token, String sessionId, String ipAddress, String userAgent, LocalDateTime expireTime);

    /**
     * 根据Token获取会话
     * 
     * @param token 访问令牌
     * @return 用户会话
     */
    UserSession getSessionByToken(String token);

    /**
     * 根据会话ID获取会话
     * 
     * @param sessionId 会话ID
     * @return 用户会话
     */
    UserSession getSessionBySessionId(String sessionId);

    /**
     * 获取用户的活跃会话列表
     * 
     * @param userId 用户ID
     * @return 活跃会话列表
     */
    List<UserSession> getActiveSessionsByUserId(Long userId);

    /**
     * 更新会话最后访问时间
     * 
     * @param sessionId 会话ID
     * @param lastAccessTime 最后访问时间
     */
    void updateLastAccessTime(String sessionId, LocalDateTime lastAccessTime);

    /**
     * 使指定用户的所有会话失效
     * 
     * @param userId 用户ID
     */
    void invalidateUserSessions(Long userId);

    /**
     * 使指定会话失效
     * 
     * @param sessionId 会话ID
     */
    void invalidateSession(String sessionId);

    /**
     * 清理过期会话
     * 
     * @return 清理的会话数量
     */
    int cleanExpiredSessions();

    /**
     * 统计在线用户数
     * 
     * @return 在线用户数
     */
    Long countOnlineUsers();

    /**
     * 检查用户是否超过最大并发会话数
     * 
     * @param userId 用户ID
     * @param maxConcurrentSessions 最大并发会话数
     * @return 是否超过限制
     */
    boolean isExceedMaxConcurrentSessions(Long userId, int maxConcurrentSessions);

    /**
     * 强制下线用户的最旧会话（当超过并发限制时）
     * 
     * @param userId 用户ID
     * @param keepCount 保留的会话数量
     */
    void forceLogoutOldestSessions(Long userId, int keepCount);
}
