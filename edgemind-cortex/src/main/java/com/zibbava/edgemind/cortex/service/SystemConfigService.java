package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.SystemConfig;

import java.util.List;
import java.util.Map;

/**
 * 系统配置服务接口
 * 
 * <AUTHOR>
 */
public interface SystemConfigService extends IService<SystemConfig> {

    /**
     * 根据配置键获取配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值并转换为指定类型
     * 
     * @param configKey 配置键
     * @param type 目标类型
     * @param <T> 类型参数
     * @return 转换后的配置值
     */
    <T> T getConfigValue(String configKey, Class<T> type);

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     * 
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值或默认值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 根据配置键获取配置值并转换为指定类型，如果不存在则返回默认值
     * 
     * @param configKey 配置键
     * @param type 目标类型
     * @param defaultValue 默认值
     * @param <T> 类型参数
     * @return 转换后的配置值或默认值
     */
    <T> T getConfigValue(String configKey, Class<T> type, T defaultValue);

    /**
     * 设置配置值
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     */
    void setConfigValue(String configKey, String configValue);

    /**
     * 设置配置值
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     * @param description 配置描述
     */
    void setConfigValue(String configKey, String configValue, String description);

    /**
     * 批量设置配置
     * 
     * @param configs 配置映射
     */
    void batchSetConfig(Map<String, String> configs);

    /**
     * 根据分类获取配置列表
     * 
     * @param category 配置分类
     * @return 配置列表
     */
    List<SystemConfig> getConfigsByCategory(String category);

    /**
     * 获取所有启用的配置
     * 
     * @return 配置列表
     */
    List<SystemConfig> getAllEnabledConfigs();

    /**
     * 获取系统配置
     * 
     * @return 系统配置列表
     */
    List<SystemConfig> getSystemConfigs();

    /**
     * 获取用户自定义配置
     * 
     * @return 用户配置列表
     */
    List<SystemConfig> getUserConfigs();

    /**
     * 删除配置
     * 
     * @param configKey 配置键
     */
    void deleteConfig(String configKey);

    /**
     * 检查配置键是否存在
     * 
     * @param configKey 配置键
     * @return 是否存在
     */
    boolean isConfigExists(String configKey);

    /**
     * 刷新配置缓存
     */
    void refreshCache();

    /**
     * 获取配置的分类列表
     * 
     * @return 分类列表
     */
    List<String> getConfigCategories();

    /**
     * 导出配置
     * 
     * @param category 配置分类（可选）
     * @return 配置列表
     */
    List<SystemConfig> exportConfigs(String category);

    /**
     * 导入配置
     * 
     * @param configs 配置列表
     * @param overwrite 是否覆盖已存在的配置
     * @return 导入结果
     */
    ImportResult importConfigs(List<SystemConfig> configs, boolean overwrite);

    /**
     * 导入结果
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private int skippedCount;
        private List<String> errorMessages;

        // 构造函数、getter、setter省略
        public ImportResult() {}

        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }

        public int getFailureCount() { return failureCount; }
        public void setFailureCount(int failureCount) { this.failureCount = failureCount; }

        public int getSkippedCount() { return skippedCount; }
        public void setSkippedCount(int skippedCount) { this.skippedCount = skippedCount; }

        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }
}
