package com.zibbava.edgemind.cortex.annotation;

import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;

import java.lang.annotation.*;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 操作类型
     */
    OperationType operationType();

    /**
     * 操作模块
     */
    String module();

    /**
     * 操作描述
     */
    String description();

    /**
     * 是否记录请求参数
     */
    boolean recordParams() default true;

    /**
     * 是否记录响应结果
     */
    boolean recordResult() default false;

    /**
     * 是否记录执行时间
     */
    boolean recordExecutionTime() default true;
}
