package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据权限实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_data_permission")
public class DataPermission {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("permission_id")
    private Long permissionId;

    @TableField("data_scope")
    private DataScope dataScope;

    @TableField("dept_ids")
    private String deptIds; // JSON格式存储部门ID列表

    @TableField("filter_sql")
    private String filterSql;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // ----- 关联关系 -----

    @TableField(exist = false)
    private Permission permission;

    @TableField(exist = false)
    private List<Long> deptIdList; // 用于前端展示和编辑

    /**
     * 数据范围枚举
     */
    public enum DataScope {
        ALL("全部数据权限"),
        DEPT("本部门数据权限"),
        DEPT_AND_SUB("本部门及以下数据权限"),
        SELF("仅本人数据权限"),
        CUSTOM("自定义数据权限");

        private final String description;

        DataScope(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
