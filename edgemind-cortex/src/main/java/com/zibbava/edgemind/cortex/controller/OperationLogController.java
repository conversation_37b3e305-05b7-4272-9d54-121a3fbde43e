package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;
import com.zibbava.edgemind.cortex.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/log")
@RequiredArgsConstructor
public class OperationLogController {

    private final OperationLogService operationLogService;

    /**
     * 分页查询操作日志
     */
    @GetMapping("/list")
    @SaCheckPermission("system:log:list")
    @OperationLog(operationType = OperationType.QUERY, module = "操作日志", description = "查询操作日志列表")
    public ResponseEntity<ApiResponse<IPage<com.zibbava.edgemind.cortex.entity.OperationLog>>> getLogPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        IPage<com.zibbava.edgemind.cortex.entity.OperationLog> result = operationLogService.getLogPage(
                pageNum, pageSize, userId, operationType, module, startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取用户最近的操作日志
     */
    @GetMapping("/recent/{userId}")
    @SaCheckPermission("system:log:list")
    @OperationLog(operationType = OperationType.QUERY, module = "操作日志", description = "查询用户最近操作日志")
    public ResponseEntity<ApiResponse<List<com.zibbava.edgemind.cortex.entity.OperationLog>>> getRecentLogsByUserId(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<com.zibbava.edgemind.cortex.entity.OperationLog> result = operationLogService.getRecentLogsByUserId(userId, limit);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 统计指定时间范围内的操作次数
     */
    @GetMapping("/count")
    @SaCheckPermission("system:log:list")
    public ResponseEntity<ApiResponse<Long>> countOperationsByTimeRange(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Long count = operationLogService.countOperationsByTimeRange(startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success("统计完成", count));
    }

    /**
     * 统计各模块的操作次数
     */
    @GetMapping("/module-statistics")
    @SaCheckPermission("system:log:list")
    public ResponseEntity<ApiResponse<List<Map<String, Object>>>> countOperationsByModule(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        List<Map<String, Object>> result = operationLogService.countOperationsByModule(startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success("统计完成", result));
    }

    /**
     * 获取操作统计数据（用于仪表板）
     */
    @GetMapping("/statistics")
    @SaCheckPermission("system:log:list")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getOperationStatistics(
            @RequestParam(defaultValue = "7") int days) {
        
        Map<String, Object> result = operationLogService.getOperationStatistics(days);
        return ResponseEntity.ok(ApiResponse.success("统计完成", result));
    }

    /**
     * 导出操作日志
     */
    @PostMapping("/export")
    @SaCheckPermission("system:log:export")
    @OperationLog(operationType = OperationType.EXPORT, module = "操作日志", description = "导出操作日志")
    public ResponseEntity<ApiResponse<List<com.zibbava.edgemind.cortex.entity.OperationLog>>> exportLogs(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) String module,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        List<com.zibbava.edgemind.cortex.entity.OperationLog> result = operationLogService.exportLogs(
                userId, operationType, module, startTime, endTime);
        return ResponseEntity.ok(ApiResponse.success("导出成功", result));
    }

    /**
     * 清理过期日志
     */
    @PostMapping("/clean")
    @SaCheckPermission("system:log:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "操作日志", description = "清理过期日志")
    public ResponseEntity<ApiResponse<Integer>> cleanExpiredLogs(@RequestParam(defaultValue = "90") int retentionDays) {
        int cleanedCount = operationLogService.cleanExpiredLogs(retentionDays);
        return ResponseEntity.ok(ApiResponse.success("清理完成", cleanedCount));
    }
}
