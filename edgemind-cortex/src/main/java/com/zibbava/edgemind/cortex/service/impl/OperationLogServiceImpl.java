package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.OperationLog;
import com.zibbava.edgemind.cortex.mapper.OperationLogMapper;
import com.zibbava.edgemind.cortex.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements OperationLogService {

    private final OperationLogMapper operationLogMapper;

    @Override
    @Async
    public void recordLog(Long userId, String username, OperationLog.OperationType operationType,
                         String module, String description, String requestUrl, String requestMethod,
                         String requestParams, String responseResult, String ipAddress,
                         String userAgent, Long executionTime, Integer status, String errorMessage) {
        try {
            OperationLog log = OperationLog.builder()
                    .userId(userId)
                    .username(username)
                    .operationType(operationType)
                    .module(module)
                    .description(description)
                    .requestUrl(requestUrl)
                    .requestMethod(requestMethod)
                    .requestParams(requestParams)
                    .responseResult(responseResult)
                    .ipAddress(ipAddress)
                    .userAgent(userAgent)
                    .executionTime(executionTime)
                    .status(status)
                    .errorMessage(errorMessage)
                    .createTime(LocalDateTime.now())
                    .build();
            
            this.save(log);
        } catch (Exception e) {
            log.error("记录操作日志失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void recordSuccessLog(Long userId, String username, OperationLog.OperationType operationType,
                                String module, String description) {
        recordLog(userId, username, operationType, module, description, 
                 null, null, null, null, null, null, null, 
                 OperationLog.Status.SUCCESS, null);
    }

    @Override
    public void recordFailureLog(Long userId, String username, OperationLog.OperationType operationType,
                                String module, String description, String errorMessage) {
        recordLog(userId, username, operationType, module, description, 
                 null, null, null, null, null, null, null, 
                 OperationLog.Status.FAILED, errorMessage);
    }

    @Override
    public IPage<OperationLog> getLogPage(Integer pageNum, Integer pageSize, Long userId,
                                         String operationType, String module,
                                         LocalDateTime startTime, LocalDateTime endTime) {
        Page<OperationLog> page = new Page<>(pageNum, pageSize);
        return operationLogMapper.selectLogPage(page, userId, operationType, module, startTime, endTime);
    }

    @Override
    public List<OperationLog> getRecentLogsByUserId(Long userId, int limit) {
        return operationLogMapper.selectRecentLogsByUserId(userId, limit);
    }

    @Override
    public Long countOperationsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return operationLogMapper.countOperationsByTimeRange(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> countOperationsByModule(LocalDateTime startTime, LocalDateTime endTime) {
        return operationLogMapper.countOperationsByModule(startTime, endTime);
    }

    @Override
    public int cleanExpiredLogs(int retentionDays) {
        LocalDateTime beforeTime = LocalDateTime.now().minusDays(retentionDays);
        return operationLogMapper.deleteLogsBefore(beforeTime);
    }

    @Override
    public List<OperationLog> exportLogs(Long userId, String operationType, String module,
                                        LocalDateTime startTime, LocalDateTime endTime) {
        // 使用分页查询避免一次性加载过多数据
        Page<OperationLog> page = new Page<>(1, 10000); // 限制最大导出数量
        IPage<OperationLog> result = operationLogMapper.selectLogPage(page, userId, operationType, module, startTime, endTime);
        return result.getRecords();
    }

    @Override
    public Map<String, Object> getOperationStatistics(int days) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(days);
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总操作次数
        Long totalOperations = countOperationsByTimeRange(startTime, endTime);
        statistics.put("totalOperations", totalOperations);
        
        // 各模块操作统计
        List<Map<String, Object>> moduleStats = countOperationsByModule(startTime, endTime);
        statistics.put("moduleStatistics", moduleStats);
        
        // 今日操作次数
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        Long todayOperations = countOperationsByTimeRange(todayStart, endTime);
        statistics.put("todayOperations", todayOperations);
        
        return statistics;
    }
}
