package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.dto.RoleManagementDTO.*;
import com.zibbava.edgemind.cortex.entity.*;
import com.zibbava.edgemind.cortex.exception.BadRequestException;
import com.zibbava.edgemind.cortex.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.mapper.RoleMapper;
import com.zibbava.edgemind.cortex.mapper.RolePermissionMapper;
import com.zibbava.edgemind.cortex.mapper.UserRoleMapper;
import com.zibbava.edgemind.cortex.service.PermissionService;
import com.zibbava.edgemind.cortex.service.RoleManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 角色管理服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleManagementServiceImpl implements RoleManagementService {

    private final RoleMapper roleMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final UserRoleMapper userRoleMapper;
    private final PermissionService permissionService;

    @Override
    public IPage<RoleResponse> getRolePage(RoleQueryRequest request) {
        Page<Role> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(request.getRoleName()), Role::getRoleName, request.getRoleName())
                   .like(StringUtils.hasText(request.getRoleCode()), Role::getRoleCode, request.getRoleCode())
                   .eq(request.getStatus() != null, Role::getStatus, request.getStatus())
                   .orderByDesc(Role::getCreateTime);
        
        IPage<Role> rolePage = roleMapper.selectPage(page, queryWrapper);
        
        // 转换为响应DTO
        IPage<RoleResponse> responsePage = rolePage.convert(this::convertToRoleResponse);
        
        return responsePage;
    }

    @Override
    public List<RoleResponse> getAllRoles() {
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getStatus, 1)
               .orderByAsc(Role::getRoleName);
        
        List<Role> roles = roleMapper.selectList(wrapper);
        return roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public RoleResponse getRoleById(Long roleId) {
        Role role = roleMapper.selectById(roleId);
        if (role == null) {
            throw new ResourceNotFoundException("角色不存在: " + roleId);
        }
        
        RoleResponse response = convertToRoleResponse(role);
        
        // 获取角色权限
        List<Permission> permissions = permissionService.findPermissionsByRoleIds(List.of(roleId));
        response.setPermissions(permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList()));
        
        // 获取用户数量
        response.setUserCount(getRoleUserCount(roleId));
        
        return response;
    }

    @Override
    @Transactional
    public Long createRole(CreateRoleRequest request) {
        // 验证角色编码唯一性
        if (isRoleCodeExists(request.getRoleCode(), null)) {
            throw new BadRequestException("角色编码已存在: " + request.getRoleCode());
        }
        
        // 验证角色名称唯一性
        if (isRoleNameExists(request.getRoleName(), null)) {
            throw new BadRequestException("角色名称已存在: " + request.getRoleName());
        }
        
        // 创建角色
        Role role = new Role();
        role.setRoleName(request.getRoleName());
        role.setRoleCode(request.getRoleCode());
        role.setDescription(request.getDescription());
        role.setStatus(1); // 默认启用
        
        roleMapper.insert(role);
        
        // 分配权限
        if (!CollectionUtils.isEmpty(request.getPermissionIds())) {
            assignPermissionsToRole(role.getId(), request.getPermissionIds());
        }
        
        return role.getId();
    }

    @Override
    @Transactional
    public void updateRole(UpdateRoleRequest request) {
        Role existingRole = roleMapper.selectById(request.getId());
        if (existingRole == null) {
            throw new ResourceNotFoundException("角色不存在: " + request.getId());
        }
        
        // 验证角色名称唯一性
        if (isRoleNameExists(request.getRoleName(), request.getId())) {
            throw new BadRequestException("角色名称已存在: " + request.getRoleName());
        }
        
        // 更新角色信息
        Role role = new Role();
        role.setId(request.getId());
        role.setRoleName(request.getRoleName());
        role.setDescription(request.getDescription());
        role.setStatus(request.getStatus());
        
        roleMapper.updateById(role);
    }

    @Override
    @Transactional
    public void deleteRole(Long roleId) {
        Role role = roleMapper.selectById(roleId);
        if (role == null) {
            throw new ResourceNotFoundException("角色不存在: " + roleId);
        }
        
        if (!canDeleteRole(roleId)) {
            throw new BadRequestException("该角色下还有用户，无法删除");
        }
        
        // 删除角色权限关联
        LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RolePermission::getRoleId, roleId);
        rolePermissionMapper.delete(wrapper);
        
        // 删除角色
        roleMapper.deleteById(roleId);
    }

    @Override
    @Transactional
    public void batchDeleteRoles(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        
        // 检查是否可以删除
        for (Long roleId : roleIds) {
            if (!canDeleteRole(roleId)) {
                Role role = roleMapper.selectById(roleId);
                String roleName = role != null ? role.getRoleName() : "ID:" + roleId;
                throw new BadRequestException("角色 " + roleName + " 下还有用户，无法删除");
            }
        }
        
        // 删除角色权限关联
        LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RolePermission::getRoleId, roleIds);
        rolePermissionMapper.delete(wrapper);
        
        // 批量删除角色
        roleMapper.deleteBatchIds(roleIds);
    }

    @Override
    @Transactional
    public void assignPermissions(AssignPermissionRequest request) {
        Role role = roleMapper.selectById(request.getRoleId());
        if (role == null) {
            throw new ResourceNotFoundException("角色不存在: " + request.getRoleId());
        }
        
        assignPermissionsToRole(request.getRoleId(), request.getPermissionIds());
    }

    @Override
    @Transactional
    public void toggleRoleStatus(ToggleStatusRequest request) {
        Role role = roleMapper.selectById(request.getRoleId());
        if (role == null) {
            throw new ResourceNotFoundException("角色不存在: " + request.getRoleId());
        }
        
        Role updateRole = new Role();
        updateRole.setId(request.getRoleId());
        updateRole.setStatus(request.getStatus());
        
        roleMapper.updateById(updateRole);
    }

    @Override
    public boolean isRoleCodeExists(String roleCode, Long excludeRoleId) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }
        
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getRoleCode, roleCode);
        if (excludeRoleId != null) {
            wrapper.ne(Role::getId, excludeRoleId);
        }
        
        return roleMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isRoleNameExists(String roleName, Long excludeRoleId) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }
        
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getRoleName, roleName);
        if (excludeRoleId != null) {
            wrapper.ne(Role::getId, excludeRoleId);
        }
        
        return roleMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<PermissionResponse> getRolePermissions(Long roleId) {
        List<Permission> permissions = permissionService.findPermissionsByRoleIds(List.of(roleId));
        return permissions.stream()
                .map(this::convertToPermissionResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<PermissionTreeNode> getPermissionTree(Long roleId) {
        // 获取所有权限
        List<Permission> allPermissions = permissionService.list();
        
        // 获取角色已有权限
        Set<Long> rolePermissionIds = Set.of();
        if (roleId != null) {
            List<Permission> rolePermissions = permissionService.findPermissionsByRoleIds(List.of(roleId));
            rolePermissionIds = rolePermissions.stream()
                    .map(Permission::getId)
                    .collect(Collectors.toSet());
        }
        
        // 构建权限树
        return buildPermissionTree(allPermissions, 0L, rolePermissionIds);
    }

    @Override
    public boolean canDeleteRole(Long roleId) {
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getRoleId, roleId);
        return userRoleMapper.selectCount(wrapper) == 0;
    }

    @Override
    public int getRoleUserCount(Long roleId) {
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getRoleId, roleId);
        return Math.toIntExact(userRoleMapper.selectCount(wrapper));
    }

    @Override
    @Transactional
    public Long copyRole(Long sourceRoleId, String newRoleName, String newRoleCode) {
        Role sourceRole = roleMapper.selectById(sourceRoleId);
        if (sourceRole == null) {
            throw new ResourceNotFoundException("源角色不存在: " + sourceRoleId);
        }
        
        // 验证新角色编码和名称的唯一性
        if (isRoleCodeExists(newRoleCode, null)) {
            throw new BadRequestException("角色编码已存在: " + newRoleCode);
        }
        if (isRoleNameExists(newRoleName, null)) {
            throw new BadRequestException("角色名称已存在: " + newRoleName);
        }
        
        // 创建新角色
        Role newRole = new Role();
        newRole.setRoleName(newRoleName);
        newRole.setRoleCode(newRoleCode);
        newRole.setDescription(sourceRole.getDescription());
        newRole.setStatus(1);
        
        roleMapper.insert(newRole);
        
        // 复制权限
        List<Permission> sourcePermissions = permissionService.findPermissionsByRoleIds(List.of(sourceRoleId));
        if (!sourcePermissions.isEmpty()) {
            List<Long> permissionIds = sourcePermissions.stream()
                    .map(Permission::getId)
                    .collect(Collectors.toList());
            assignPermissionsToRole(newRole.getId(), permissionIds);
        }
        
        return newRole.getId();
    }

    /**
     * 为角色分配权限
     */
    private void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        // 删除现有权限关联
        LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RolePermission::getRoleId, roleId);
        rolePermissionMapper.delete(wrapper);
        
        // 添加新的权限关联
        if (!CollectionUtils.isEmpty(permissionIds)) {
            List<RolePermission> rolePermissions = permissionIds.stream()
                    .map(permissionId -> new RolePermission(roleId, permissionId))
                    .collect(Collectors.toList());
            
            for (RolePermission rolePermission : rolePermissions) {
                rolePermissionMapper.insert(rolePermission);
            }
        }
    }

    /**
     * 转换为角色响应DTO
     */
    private RoleResponse convertToRoleResponse(Role role) {
        return RoleResponse.builder()
                .id(role.getId())
                .roleName(role.getRoleName())
                .roleCode(role.getRoleCode())
                .description(role.getDescription())
                .status(role.getStatus())
                .createTime(role.getCreateTime())
                .updateTime(role.getUpdateTime())
                .build();
    }

    /**
     * 转换为权限响应DTO
     */
    private PermissionResponse convertToPermissionResponse(Permission permission) {
        return PermissionResponse.builder()
                .id(permission.getId())
                .permissionName(permission.getPermissionName())
                .permissionCode(permission.getPermissionCode())
                .type(permission.getType().name())
                .parentId(permission.getParentId())
                .description(permission.getDescription())
                .icon(permission.getIcon())
                .sortOrder(permission.getSortOrder())
                .build();
    }

    /**
     * 构建权限树
     */
    private List<PermissionTreeNode> buildPermissionTree(List<Permission> allPermissions, Long parentId, Set<Long> checkedIds) {
        List<PermissionTreeNode> result = new ArrayList<>();
        
        // 按父ID分组
        Map<Long, List<Permission>> permissionMap = allPermissions.stream()
                .collect(Collectors.groupingBy(perm -> perm.getParentId() == null ? 0L : perm.getParentId()));
        
        List<Permission> children = permissionMap.get(parentId);
        if (children != null) {
            for (Permission permission : children) {
                PermissionTreeNode node = PermissionTreeNode.builder()
                        .id(permission.getId())
                        .label(permission.getPermissionName())
                        .value(permission.getPermissionCode())
                        .type(permission.getType().name())
                        .parentId(permission.getParentId())
                        .icon(permission.getIcon())
                        .sortOrder(permission.getSortOrder())
                        .checked(checkedIds.contains(permission.getId()))
                        .children(buildPermissionTree(allPermissions, permission.getId(), checkedIds))
                        .build();
                
                result.add(node);
            }
        }
        
        return result;
    }
}
