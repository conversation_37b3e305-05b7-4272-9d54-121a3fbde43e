package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.Department;

import java.util.List;

public interface DepartmentService extends IService<Department> {

    /**
     * 获取部门树
     * @return 部门树结构的列表
     */
    List<Department> getDepartmentTree();

    /**
     * 根据父部门ID获取子部门列表
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> getChildDepartments(Long parentId);

    /**
     * 创建部门
     *
     * @param department 部门信息
     * @return 部门ID
     */
    Long createDepartment(Department department);

    /**
     * 更新部门信息
     *
     * @param department 部门信息
     */
    void updateDepartment(Department department);

    /**
     * 删除部门
     *
     * @param deptId 部门ID
     */
    void deleteDepartment(Long deptId);

    /**
     * 检查部门编码是否存在
     *
     * @param deptCode 部门编码
     * @param excludeDeptId 排除的部门ID
     * @return 是否存在
     */
    boolean isDeptCodeExists(String deptCode, Long excludeDeptId);

    /**
     * 检查部门是否可以删除（是否有子部门或用户）
     *
     * @param deptId 部门ID
     * @return 是否可以删除
     */
    boolean canDeleteDepartment(Long deptId);

    /**
     * 获取部门路径
     *
     * @param deptId 部门ID
     * @return 部门路径
     */
    String getDepartmentPath(Long deptId);

    /**
     * 更新部门路径
     *
     * @param deptId 部门ID
     */
    void updateDepartmentPath(Long deptId);

    /**
     * 获取部门的所有后代部门ID列表
     *
     * @param deptId 部门ID
     * @return 后代部门ID列表
     */
    List<Long> getDescendantDeptIds(Long deptId);
}