package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统配置实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_config")
public class SystemConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("config_key")
    private String configKey;

    @TableField("config_value")
    private String configValue;

    @TableField("config_type")
    private ConfigType configType;

    @TableField("category")
    private String category;

    @TableField("description")
    private String description;

    @TableField("is_system")
    private Boolean isSystem;

    @TableField("status")
    private Integer status;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        STRING("字符串"),
        NUMBER("数字"),
        BOOLEAN("布尔值"),
        JSON("JSON对象");

        private final String description;

        ConfigType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 状态常量
     */
    public static class Status {
        public static final int DISABLED = 0;
        public static final int ENABLED = 1;
    }

    /**
     * 获取配置值并转换为指定类型
     */
    public <T> T getValueAs(Class<T> type) {
        if (configValue == null) {
            return null;
        }

        try {
            if (type == String.class) {
                return type.cast(configValue);
            } else if (type == Integer.class) {
                return type.cast(Integer.valueOf(configValue));
            } else if (type == Long.class) {
                return type.cast(Long.valueOf(configValue));
            } else if (type == Boolean.class) {
                return type.cast(Boolean.valueOf(configValue));
            } else if (type == Double.class) {
                return type.cast(Double.valueOf(configValue));
            }
        } catch (Exception e) {
            // 转换失败时返回null
        }
        return null;
    }
}
