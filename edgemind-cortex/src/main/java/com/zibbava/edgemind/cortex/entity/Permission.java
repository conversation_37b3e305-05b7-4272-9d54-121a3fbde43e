package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@TableName("sys_permission")
public class Permission {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("permission_name")
    private String permissionName; // 权限名称, e.g., "用户管理", "新增用户"

    @TableField("permission_code")
    private String permissionCode; // 权限编码, e.g., "user:manage", "user:create", 用于 Sa-Token

    @TableField("resource_url")
    private String resourceUrl; // 资源URL路径

    @TableField("http_method")
    private String httpMethod; // HTTP方法(GET,POST,PUT,DELETE)

    @TableField("icon")
    private String icon; // 菜单图标

    @TableField("sort_order")
    private Integer sortOrder; // 排序顺序

    @TableField("is_external")
    private Boolean isExternal; // 是否外部链接

    @TableField("component_path")
    private String componentPath; // 前端组件路径

    @TableField("type")
    @EnumValue // MyBatis Plus 枚举映射: 存储枚举的名称或指定的值
    private PermissionType type; // 权限类型 (MENU, BUTTON, API, DATA)

    @TableField("parent_id")
    private Long parentId; // 父权限ID (用于构建树形结构)

    @TableField("description")
    private String description; // 权限描述

    @TableField("status")
    private Integer status; // 状态 (0: 禁用, 1: 启用)

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // ----- 关联关系 -----

    // 与角色是多对多 (通过 RolePermission 关联)
    @TableField(exist = false) // 不在数据库表中
    private Set<Role> roles; // 拥有此权限的角色 (查询时填充)

    // 用于构建树形结构
    @TableField(exist = false)
    private List<Permission> children;

    // ----- 枚举类型 -----
    public enum PermissionType {
        MENU, BUTTON, API, DATA
    }
} 