package com.zibbava.edgemind.cortex.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户管理相关DTO
 * 
 * <AUTHOR>
 */
public class UserManagementDTO {

    /**
     * 用户创建请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateUserRequest {
        
        @NotBlank(message = "用户名不能为空")
        @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
        @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
        private String username;

        @NotBlank(message = "密码不能为空")
        @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
        private String password;

        @Size(max = 50, message = "昵称长度不能超过50个字符")
        private String nickname;

        @Email(message = "邮箱格式不正确")
        @Size(max = 100, message = "邮箱长度不能超过100个字符")
        private String email;

        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
        private String phone;

        private Long deptId;

        private List<Long> roleIds;

        @Size(max = 255, message = "备注长度不能超过255个字符")
        private String remark;
    }

    /**
     * 用户更新请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateUserRequest {
        
        @NotNull(message = "用户ID不能为空")
        private Long id;

        @Size(max = 50, message = "昵称长度不能超过50个字符")
        private String nickname;

        @Email(message = "邮箱格式不正确")
        @Size(max = 100, message = "邮箱长度不能超过100个字符")
        private String email;

        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
        private String phone;

        private Long deptId;

        @Size(max = 255, message = "备注长度不能超过255个字符")
        private String remark;

        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }

    /**
     * 用户查询请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserQueryRequest {
        
        private String username;
        private String nickname;
        private String email;
        private String phone;
        private Long deptId;
        private Integer status;
        
        @Min(value = 1, message = "页码必须大于0")
        private Integer pageNum = 1;
        
        @Min(value = 1, message = "页面大小必须大于0")
        @Max(value = 100, message = "页面大小不能超过100")
        private Integer pageSize = 10;
    }

    /**
     * 用户响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserResponse {
        
        private Long id;
        private String username;
        private String nickname;
        private String email;
        private String phone;
        private Integer status;
        private Long deptId;
        private String deptName;
        private String remark;
        private String avatar;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime lastLoginTime;
        
        private String lastLoginIp;
        
        private Boolean accountLocked;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime createTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime updateTime;
        
        private List<RoleResponse> roles;
    }

    /**
     * 角色响应DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoleResponse {
        
        private Long id;
        private String roleName;
        private String roleCode;
        private String description;
        private Integer status;
    }

    /**
     * 重置密码请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResetPasswordRequest {
        
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @NotBlank(message = "新密码不能为空")
        @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
        private String newPassword;
    }

    /**
     * 分配角色请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssignRoleRequest {
        
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @NotEmpty(message = "角色ID列表不能为空")
        private List<Long> roleIds;
    }

    /**
     * 用户状态切换请求DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ToggleStatusRequest {
        
        @NotNull(message = "用户ID不能为空")
        private Long userId;

        @NotNull(message = "状态不能为空")
        @Min(value = 0, message = "状态值不正确")
        @Max(value = 1, message = "状态值不正确")
        private Integer status;
    }
}
