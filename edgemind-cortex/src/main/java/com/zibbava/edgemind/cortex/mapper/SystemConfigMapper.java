package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 系统配置 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface SystemConfigMapper extends BaseMapper<SystemConfig> {

    /**
     * 根据配置键查询配置
     * 
     * @param configKey 配置键
     * @return 系统配置
     */
    @Select("SELECT * FROM sys_config WHERE config_key = #{configKey} AND status = 1")
    SystemConfig selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 根据分类查询配置列表
     * 
     * @param category 配置分类
     * @return 配置列表
     */
    @Select("SELECT * FROM sys_config WHERE category = #{category} AND status = 1 ORDER BY config_key")
    List<SystemConfig> selectByCategory(@Param("category") String category);

    /**
     * 查询所有启用的配置
     * 
     * @return 配置列表
     */
    @Select("SELECT * FROM sys_config WHERE status = 1 ORDER BY category, config_key")
    List<SystemConfig> selectAllEnabled();

    /**
     * 查询系统配置
     * 
     * @return 系统配置列表
     */
    @Select("SELECT * FROM sys_config WHERE is_system = 1 AND status = 1 ORDER BY config_key")
    List<SystemConfig> selectSystemConfigs();

    /**
     * 查询用户自定义配置
     * 
     * @return 用户配置列表
     */
    @Select("SELECT * FROM sys_config WHERE is_system = 0 AND status = 1 ORDER BY config_key")
    List<SystemConfig> selectUserConfigs();
}
