package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 系统管理页面控制器
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/system")
public class SystemPageController {

    /**
     * 用户管理页面
     */
    @GetMapping("/user/content")
    @SaCheckPermission("menu:system:user")
    public String userManagement() {
        return "system/user_management";
    }

    /**
     * 角色管理页面
     */
    @GetMapping("/role/content")
    @SaCheckPermission("menu:system:role")
    public String roleManagement() {
        return "system/role_management";
    }

    /**
     * 权限管理页面
     */
    @GetMapping("/permission/content")
    @SaCheckPermission("menu:system:permission")
    public String permissionManagement() {
        return "system/permission_management";
    }

    /**
     * 部门管理页面
     */
    @GetMapping("/dept/content")
    @SaCheckPermission("menu:system:dept")
    public String departmentManagement() {
        return "system/department_management";
    }

    /**
     * 操作日志页面
     */
    @GetMapping("/log/content")
    @SaCheckPermission("menu:system:log")
    public String operationLog() {
        return "system/operation_log";
    }
}
