package com.zibbava.edgemind.cortex.dto;

import lombok.Data;

@Data
public class ApiResponse<T> {

    private int code;
    private String message;
    private T data;
    private boolean success;

    private ApiResponse(int code, String message, T data, boolean success) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
    }

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data, true);
    }
     public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data, true);
    }

    public static ApiResponse<Void> success() {
        return new ApiResponse<>(200, "操作成功", null, true);
    }

     public static ApiResponse<Void> success(String message) {
        return new ApiResponse<>(200, message, null, true);
    }

    public static <T> ApiResponse<T> error(int code, String message) {
        return new ApiResponse<>(code, message, null, false);
    }

    // 可以添加更多常用的错误响应静态方法
    public static ApiResponse<Void> badRequest(String message) {
        return new ApiResponse<>(400, message, null, false);
    }

    public static ApiResponse<Void> unauthorized(String message) {
        return new ApiResponse<>(401, message, null, false);
    }

    public static ApiResponse<Void> internalError(String message) {
        return new ApiResponse<>(500, message, null, false);
    }
} 