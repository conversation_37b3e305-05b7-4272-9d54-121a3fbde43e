package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.cortex.dto.RoleManagementDTO.*;

import java.util.List;

/**
 * 角色管理服务接口
 * 
 * <AUTHOR>
 */
public interface RoleManagementService {

    /**
     * 分页查询角色列表
     * 
     * @param request 查询请求
     * @return 分页结果
     */
    IPage<RoleResponse> getRolePage(RoleQueryRequest request);

    /**
     * 获取所有角色列表（用于下拉选择）
     * 
     * @return 角色列表
     */
    List<RoleResponse> getAllRoles();

    /**
     * 根据ID获取角色详情
     * 
     * @param roleId 角色ID
     * @return 角色详情
     */
    RoleResponse getRoleById(Long roleId);

    /**
     * 创建角色
     * 
     * @param request 创建请求
     * @return 角色ID
     */
    Long createRole(CreateRoleRequest request);

    /**
     * 更新角色信息
     * 
     * @param request 更新请求
     */
    void updateRole(UpdateRoleRequest request);

    /**
     * 删除角色
     * 
     * @param roleId 角色ID
     */
    void deleteRole(Long roleId);

    /**
     * 批量删除角色
     * 
     * @param roleIds 角色ID列表
     */
    void batchDeleteRoles(List<Long> roleIds);

    /**
     * 为角色分配权限
     * 
     * @param request 分配权限请求
     */
    void assignPermissions(AssignPermissionRequest request);

    /**
     * 切换角色状态（启用/禁用）
     * 
     * @param request 状态切换请求
     */
    void toggleRoleStatus(ToggleStatusRequest request);

    /**
     * 检查角色编码是否存在
     * 
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isRoleCodeExists(String roleCode, Long excludeRoleId);

    /**
     * 检查角色名称是否存在
     * 
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isRoleNameExists(String roleName, Long excludeRoleId);

    /**
     * 获取角色的权限列表
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<PermissionResponse> getRolePermissions(Long roleId);

    /**
     * 获取权限树（用于角色权限分配）
     * 
     * @param roleId 角色ID（可选，用于标记已选中的权限）
     * @return 权限树
     */
    List<PermissionTreeNode> getPermissionTree(Long roleId);

    /**
     * 检查角色是否可以删除（是否有用户关联）
     * 
     * @param roleId 角色ID
     * @return 是否可以删除
     */
    boolean canDeleteRole(Long roleId);

    /**
     * 获取角色关联的用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    int getRoleUserCount(Long roleId);

    /**
     * 复制角色（创建一个具有相同权限的新角色）
     * 
     * @param sourceRoleId 源角色ID
     * @param newRoleName 新角色名称
     * @param newRoleCode 新角色编码
     * @return 新角色ID
     */
    Long copyRole(Long sourceRoleId, String newRoleName, String newRoleCode);
}
