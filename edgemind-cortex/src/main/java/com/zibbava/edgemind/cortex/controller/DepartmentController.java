package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 部门管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/dept")
@RequiredArgsConstructor
@Validated
public class DepartmentController {

    private final DepartmentService departmentService;

    /**
     * 获取部门树
     */
    @GetMapping("/tree")
    @SaCheckPermission("dept:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "部门管理", description = "查询部门树")
    public ResponseEntity<ApiResponse<List<Department>>> getDepartmentTree() {
        List<Department> result = departmentService.getDepartmentTree();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取所有部门列表（用于下拉选择）
     */
    @GetMapping("/all")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<ApiResponse<List<Department>>> getAllDepartments() {
        List<Department> result = departmentService.list();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据ID获取部门详情
     */
    @GetMapping("/{deptId}")
    @SaCheckPermission("dept:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "部门管理", description = "查询部门详情")
    public ResponseEntity<ApiResponse<Department>> getDepartmentById(@PathVariable Long deptId) {
        Department result = departmentService.getById(deptId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 创建部门
     */
    @PostMapping
    @SaCheckPermission("dept:manage:create")
    @OperationLog(operationType = OperationType.CREATE, module = "部门管理", description = "创建部门")
    public ResponseEntity<ApiResponse<Long>> createDepartment(@Valid @RequestBody Department department) {
        Long deptId = departmentService.createDepartment(department);
        return ResponseEntity.ok(ApiResponse.success("部门创建成功", deptId));
    }

    /**
     * 更新部门信息
     */
    @PutMapping("/{deptId}")
    @SaCheckPermission("dept:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "部门管理", description = "更新部门信息")
    public ResponseEntity<ApiResponse<Void>> updateDepartment(@PathVariable Long deptId,
                                                             @Valid @RequestBody Department department) {
        department.setId(deptId);
        departmentService.updateDepartment(department);
        return ResponseEntity.ok(ApiResponse.success("部门信息更新成功"));
    }

    /**
     * 删除部门
     */
    @DeleteMapping("/{deptId}")
    @SaCheckPermission("dept:manage:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "部门管理", description = "删除部门")
    public ResponseEntity<ApiResponse<Void>> deleteDepartment(@PathVariable Long deptId) {
        departmentService.deleteDepartment(deptId);
        return ResponseEntity.ok(ApiResponse.success("部门删除成功"));
    }

    /**
     * 检查部门编码是否存在
     */
    @GetMapping("/check-code")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> checkDeptCode(@RequestParam String deptCode,
                                                             @RequestParam(required = false) Long excludeDeptId) {
        boolean exists = departmentService.isDeptCodeExists(deptCode, excludeDeptId);
        return ResponseEntity.ok(ApiResponse.success("检查完成", exists));
    }

    /**
     * 检查部门是否可以删除
     */
    @GetMapping("/{deptId}/can-delete")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> canDeleteDepartment(@PathVariable Long deptId) {
        boolean canDelete = departmentService.canDeleteDepartment(deptId);
        return ResponseEntity.ok(ApiResponse.success("检查完成", canDelete));
    }

    /**
     * 获取部门路径
     */
    @GetMapping("/{deptId}/path")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<ApiResponse<String>> getDepartmentPath(@PathVariable Long deptId) {
        String path = departmentService.getDepartmentPath(deptId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", path));
    }

    /**
     * 根据父部门ID获取子部门列表
     */
    @GetMapping("/children/{parentId}")
    @SaCheckPermission("dept:manage:list")
    public ResponseEntity<ApiResponse<List<Department>>> getChildDepartments(@PathVariable Long parentId) {
        List<Department> result = departmentService.getChildDepartments(parentId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }
}
