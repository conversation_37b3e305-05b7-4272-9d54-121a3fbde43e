package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.zibbava.edgemind.cortex.config.OnlyOfficeConfig;
import com.zibbava.edgemind.cortex.service.ModelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
@RequiredArgsConstructor
public class WebController {

    private final OnlyOfficeConfig onlyOfficeConfig;
    private final ModelService modelService;

    // 主布局页面
    @SaIgnore
    @GetMapping("/")
    public String layout() {
        // 这里可以根据需要向布局传递通用模型属性
        return "layout";
    }

    // --- 内容片段路由 (供 iframe 加载) ---

    @SaIgnore
    @GetMapping("/chat/content")
    public String chatContent(Model model) {
        model.addAttribute("currentPage", "chat"); // 用于可能的片段内部逻辑
        return "chat"; // 直接返回 chat.html 整页，不再返回片段
    }

    @SaIgnore
    @GetMapping("/knowledge/content")
    public String knowledgeBaseContent(Model model) {
        model.addAttribute("currentPage", "knowledge");
        // 添加 ONLYOFFICE API URL
        model.addAttribute("onlyofficeApiUrl", onlyOfficeConfig.getApiJsUrl());
        return "knowledge_base"; // 返回 knowledge_base.html 中的 contentBody 片段
    }

    @SaIgnore
    @GetMapping("/tools/content")
    public String toolsContent(Model model) {
        model.addAttribute("currentPage", "tools");
        return "tools"; // 返回 tools.html 中的 contentBody 片段
    }

    // --- 系统设置页面路由 ---

    @SaIgnore
    @GetMapping("/settings/content")
    public String settingsContent(Model model) {
        model.addAttribute("currentPage", "settings");
        return "settings"; // 返回系统设置页面
    }

    // --- 系统授权页面路由 ---

    @SaIgnore
    @GetMapping("/license/content")
    public String licenseContent(Model model) {
        model.addAttribute("currentPage", "license");
        return "license"; // 返回系统授权页面
    }

    // --- 新 AI 知识库路由 ---

    @GetMapping("/new-knowledge/team/content")
    public String newKnowledgeTeamContent(Model model, @RequestParam(value = "init", required = false, defaultValue = "false") boolean init) {
        model.addAttribute("knowledgeType", "team"); // 传递类型参数
        model.addAttribute("currentPage", "new_knowledge_team");
        model.addAttribute("initSpace", init); // 是否需要初始化知识空间
        // 添加 ONLYOFFICE API URL
        model.addAttribute("onlyofficeApiUrl", onlyOfficeConfig.getApiJsUrl());
        return "knowledge_base";
    }

    @GetMapping("/new-knowledge/private/content")
    public String newKnowledgePrivateContent(Model model, @RequestParam(value = "init", required = false, defaultValue = "false") boolean init) {
        model.addAttribute("knowledgeType", "private"); // 传递类型参数
        model.addAttribute("currentPage", "new_knowledge_private");
        model.addAttribute("initSpace", init); // 是否需要初始化知识空间
        // 添加 ONLYOFFICE API URL
        model.addAttribute("onlyofficeApiUrl", onlyOfficeConfig.getApiJsUrl());
        return "knowledge_base";
    }


    /**
     * 模型下载中心页面
     *
     * @param model 模型数据
     * @return 模型下载页面视图
     */
    @GetMapping("/models")
    public String modelDownloaderPage(Model model) {
        // 添加已安装模型列表到模型数据
        model.addAttribute("installedModels", modelService.getInstalledModels());

        // 添加可用模型列表到模型数据
        model.addAttribute("availableModels", modelService.getAvailableModels());

        return "model_downloader";
    }

    // --- 为了兼容旧的直接访问路径（可选，可以重定向或移除） ---
    @SaIgnore
    @GetMapping("/chat")
    public String redirectToLayoutChat() {
        return "redirect:/"; // 重定向到根路径，它会加载 layout 并默认加载 chat content
    }

    @SaIgnore
    @GetMapping("/tools")
    public String redirectToLayoutTools() {
        return "redirect:/?page=tools"; // 示例：通过参数传递
    }

    // --- 系统设置页面的直接访问路径 ---

    @SaIgnore
    @GetMapping("/settings")
    public String redirectToSettings() {
        return "redirect:/?page=settings"; // 通过参数传递加载系统设置页面
    }

    // --- 系统授权页面的直接访问路径 ---

    @SaIgnore
    @GetMapping("/license")
    public String redirectToLicense() {
        return "redirect:/?page=license"; // 通过参数传递加载系统授权页面
    }
}