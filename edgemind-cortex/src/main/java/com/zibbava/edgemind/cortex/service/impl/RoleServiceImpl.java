package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.entity.RolePermission;
import com.zibbava.edgemind.cortex.mapper.RoleMapper;
import com.zibbava.edgemind.cortex.mapper.RolePermissionMapper;
import com.zibbava.edgemind.cortex.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    private final RoleMapper roleMapper;
    private final RolePermissionMapper rolePermissionMapper;

    @Override
    public List<Role> findRolesByUserId(Long userId) {
        return roleMapper.selectByUserId(userId);
    }

    @Override
    public List<Role> findRolesByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return roleMapper.selectByUserIds(userIds);
    }

    @Override
    @Transactional
    public void assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        // 删除现有权限关联
        LambdaQueryWrapper<RolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RolePermission::getRoleId, roleId);
        rolePermissionMapper.delete(wrapper);

        // 添加新的权限关联
        if (!CollectionUtils.isEmpty(permissionIds)) {
            List<RolePermission> rolePermissions = permissionIds.stream()
                    .map(permissionId -> new RolePermission(roleId, permissionId))
                    .collect(Collectors.toList());

            for (RolePermission rolePermission : rolePermissions) {
                rolePermissionMapper.insert(rolePermission);
            }
        }
    }

    @Override
    public List<Long> getPermissionIdsByRoleId(Long roleId) {
        return rolePermissionMapper.selectPermissionIdsByRoleId(roleId);
    }

    @Override
    public Role findByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return null;
        }
        return roleMapper.selectByRoleCode(roleCode);
    }

    @Override
    public boolean isRoleCodeExists(String roleCode, Long excludeRoleId) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }

        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getRoleCode, roleCode);
        if (excludeRoleId != null) {
            wrapper.ne(Role::getId, excludeRoleId);
        }

        return this.count(wrapper) > 0;
    }

    @Override
    public boolean isRoleNameExists(String roleName, Long excludeRoleId) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }

        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getRoleName, roleName);
        if (excludeRoleId != null) {
            wrapper.ne(Role::getId, excludeRoleId);
        }

        return this.count(wrapper) > 0;
    }

    @Override
    public List<Role> getAllEnabledRoles() {
        LambdaQueryWrapper<Role> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Role::getStatus, 1)
               .orderByAsc(Role::getRoleName);
        
        return this.list(wrapper);
    }
}
