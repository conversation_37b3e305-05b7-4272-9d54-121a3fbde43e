package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.RoleManagementDTO.*;
import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;
import com.zibbava.edgemind.cortex.service.RoleManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 角色管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/role")
@RequiredArgsConstructor
@Validated
public class RoleManagementController {

    private final RoleManagementService roleManagementService;

    /**
     * 分页查询角色列表
     */
    @GetMapping("/list")
    @SaCheckPermission("role:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "角色管理", description = "查询角色列表")
    public ResponseEntity<ApiResponse<IPage<RoleResponse>>> getRolePage(@Valid RoleQueryRequest request) {
        IPage<RoleResponse> result = roleManagementService.getRolePage(request);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取所有角色列表（用于下拉选择）
     */
    @GetMapping("/all")
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<ApiResponse<List<RoleResponse>>> getAllRoles() {
        List<RoleResponse> result = roleManagementService.getAllRoles();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取角色详情
     */
    @GetMapping("/{roleId}")
    @SaCheckPermission("role:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "角色管理", description = "查询角色详情")
    public ResponseEntity<ApiResponse<RoleResponse>> getRoleById(@PathVariable Long roleId) {
        RoleResponse result = roleManagementService.getRoleById(roleId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 创建角色
     */
    @PostMapping
    @SaCheckPermission("role:manage:create")
    @OperationLog(operationType = OperationType.CREATE, module = "角色管理", description = "创建角色")
    public ResponseEntity<ApiResponse<Long>> createRole(@Valid @RequestBody CreateRoleRequest request) {
        Long roleId = roleManagementService.createRole(request);
        return ResponseEntity.ok(ApiResponse.success("角色创建成功", roleId));
    }

    /**
     * 更新角色信息
     */
    @PutMapping("/{roleId}")
    @SaCheckPermission("role:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "角色管理", description = "更新角色信息")
    public ResponseEntity<ApiResponse<Void>> updateRole(@PathVariable Long roleId,
                                                       @Valid @RequestBody UpdateRoleRequest request) {
        request.setId(roleId);
        roleManagementService.updateRole(request);
        return ResponseEntity.ok(ApiResponse.success("角色信息更新成功"));
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleId}")
    @SaCheckPermission("role:manage:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "角色管理", description = "删除角色")
    public ResponseEntity<ApiResponse<Void>> deleteRole(@PathVariable Long roleId) {
        roleManagementService.deleteRole(roleId);
        return ResponseEntity.ok(ApiResponse.success("角色删除成功"));
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping("/batch")
    @SaCheckPermission("role:manage:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "角色管理", description = "批量删除角色")
    public ResponseEntity<ApiResponse<Void>> batchDeleteRoles(@RequestBody List<Long> roleIds) {
        roleManagementService.batchDeleteRoles(roleIds);
        return ResponseEntity.ok(ApiResponse.success("角色批量删除成功"));
    }

    /**
     * 为角色分配权限
     */
    @PostMapping("/{roleId}/permissions")
    @SaCheckPermission("role:manage:assign-permission")
    @OperationLog(operationType = OperationType.GRANT_PERMISSION, module = "角色管理", description = "分配角色权限")
    public ResponseEntity<ApiResponse<Void>> assignPermissions(@PathVariable Long roleId,
                                                              @Valid @RequestBody AssignPermissionRequest request) {
        request.setRoleId(roleId);
        roleManagementService.assignPermissions(request);
        return ResponseEntity.ok(ApiResponse.success("权限分配成功"));
    }

    /**
     * 切换角色状态
     */
    @PostMapping("/{roleId}/toggle-status")
    @SaCheckPermission("role:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "角色管理", description = "切换角色状态")
    public ResponseEntity<ApiResponse<Void>> toggleRoleStatus(@PathVariable Long roleId,
                                                             @Valid @RequestBody ToggleStatusRequest request) {
        request.setRoleId(roleId);
        roleManagementService.toggleRoleStatus(request);
        return ResponseEntity.ok(ApiResponse.success("角色状态更新成功"));
    }

    /**
     * 检查角色编码是否存在
     */
    @GetMapping("/check-code")
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> checkRoleCode(@RequestParam String roleCode,
                                                             @RequestParam(required = false) Long excludeRoleId) {
        boolean exists = roleManagementService.isRoleCodeExists(roleCode, excludeRoleId);
        return ResponseEntity.ok(ApiResponse.success("检查完成", exists));
    }

    /**
     * 获取角色的权限列表
     */
    @GetMapping("/{roleId}/permissions")
    @SaCheckPermission("role:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "角色管理", description = "查询角色权限")
    public ResponseEntity<ApiResponse<List<PermissionResponse>>> getRolePermissions(@PathVariable Long roleId) {
        List<PermissionResponse> permissions = roleManagementService.getRolePermissions(roleId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", permissions));
    }

    /**
     * 获取权限树（用于角色权限分配）
     */
    @GetMapping("/permission-tree")
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<ApiResponse<List<PermissionTreeNode>>> getPermissionTree(@RequestParam(required = false) Long roleId) {
        List<PermissionTreeNode> tree = roleManagementService.getPermissionTree(roleId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", tree));
    }

    /**
     * 复制角色
     */
    @PostMapping("/{roleId}/copy")
    @SaCheckPermission("role:manage:create")
    @OperationLog(operationType = OperationType.CREATE, module = "角色管理", description = "复制角色")
    public ResponseEntity<ApiResponse<Long>> copyRole(@PathVariable Long roleId,
                                                     @RequestParam String newRoleName,
                                                     @RequestParam String newRoleCode) {
        Long newRoleId = roleManagementService.copyRole(roleId, newRoleName, newRoleCode);
        return ResponseEntity.ok(ApiResponse.success("角色复制成功", newRoleId));
    }

    /**
     * 检查角色是否可以删除
     */
    @GetMapping("/{roleId}/can-delete")
    @SaCheckPermission("role:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> canDeleteRole(@PathVariable Long roleId) {
        boolean canDelete = roleManagementService.canDeleteRole(roleId);
        return ResponseEntity.ok(ApiResponse.success("检查完成", canDelete));
    }
}
