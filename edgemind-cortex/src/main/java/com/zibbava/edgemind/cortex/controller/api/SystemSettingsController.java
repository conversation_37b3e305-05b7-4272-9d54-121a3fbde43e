package com.zibbava.edgemind.cortex.controller.api;

import com.zibbava.edgemind.cortex.common.Result;
import com.zibbava.edgemind.cortex.common.constants.SettingKeys;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.dto.PersonalStoragePathDTO;
import com.zibbava.edgemind.cortex.dto.SyncStatusResponseDTO;
import com.zibbava.edgemind.cortex.dto.SystemSettingsDTO;
import com.zibbava.edgemind.cortex.dto.SystemSettingsResponseDTO;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 系统设置API控制器
 * 处理系统设置相关的API请求
 */
@RestController
@RequestMapping("/api/settings")
@RequiredArgsConstructor
@Slf4j
public class SystemSettingsController {

    private final SystemSettingsService systemSettingsService;

    /**
     * 获取系统设置信息
     * @return 系统设置信息
     */
    @GetMapping
    @SaCheckPermission("system:config:list")
    public Result<SystemSettingsResponseDTO> getSettings() {
        try {
            // 构建响应DTO
            SystemSettingsResponseDTO responseDTO = SystemSettingsResponseDTO.builder()
                    .version(systemSettingsService.getSettingValue(SettingKeys.System.VERSION, "1.0.0"))
                    .systemId(systemSettingsService.getSystemIdentifier())
                    .storagePath(systemSettingsService.getKnowledgeStoragePath())
                    .personalStoragePath(systemSettingsService.getPersonalKnowledgeStoragePath())
                    .build();

            // 获取同步状态
            Map<String, Object> syncStatus = systemSettingsService.getKnowledgeSyncStatus();
            responseDTO.setSyncStatus((Integer) syncStatus.get("syncStatus"));
            responseDTO.setSyncMessage((String) syncStatus.get("syncMessage"));
            responseDTO.setLastSyncTime((LocalDateTime) syncStatus.get("lastSyncTime"));

            return Result.success(responseDTO);
        } catch (Exception e) {
            log.error("获取系统设置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取系统设置失败: " + e.getMessage());
        }
    }

    /**
     * 更新系统设置
     * @param settingsDTO 系统设置信息
     * @return 更新结果
     */
    @PostMapping
    public Result<Void> updateSettings(@RequestBody @Validated SystemSettingsDTO settingsDTO) {
        try {
            log.info("更新系统设置: {}", settingsDTO);

            // 更新知识库存储路径
            systemSettingsService.updateKnowledgeStoragePath(settingsDTO.getStoragePath());

            return Result.success();
        } catch (IllegalArgumentException e) {
            log.warn("更新系统设置参数错误: {}", e.getMessage());
            throw new BusinessException(ResultCode.PARAM_INVALID, e.getMessage());
        } catch (Exception e) {
            log.error("更新系统设置失败", e);
            throw new BusinessException(ResultCode.OPERATION_FAILED, "更新系统设置失败: " + e.getMessage());
        }
    }

    /**
     * 更新个人库存储路径
     * @param pathDTO 个人库存储路径信息
     * @return 更新结果
     */
    @PostMapping("/personal")
    public Result<Void> updatePersonalStoragePath(@RequestBody @Validated PersonalStoragePathDTO pathDTO) {
        try {
            log.info("更新个人库存储路径: {}", pathDTO);

            // 更新个人库存储路径
            systemSettingsService.updatePersonalKnowledgeStoragePath(pathDTO.getPersonalStoragePath());

            return Result.success();
        } catch (IllegalArgumentException e) {
            log.warn("更新个人库存储路径参数错误: {}", e.getMessage());
            throw new BusinessException(ResultCode.PARAM_INVALID, e.getMessage());
        } catch (Exception e) {
            log.error("更新个人库存储路径失败", e);
            throw new BusinessException(ResultCode.OPERATION_FAILED, "更新个人库存储路径失败: " + e.getMessage());
        }
    }

    /**
     * 同步知识库
     * @return 同步结果
     */
    @PostMapping("/sync")
    public Result<Void> syncKnowledgeBase() {
        try {
            log.info("开始同步知识库");
            systemSettingsService.syncKnowledgeBase();
            return Result.success();
        } catch (Exception e) {
            log.error("同步知识库失败", e);
            throw new BusinessException(ResultCode.OPERATION_FAILED, "同步知识库失败: " + e.getMessage());
        }
    }

    /**
     * 获取知识库同步状态
     * @return 同步状态
     */
    @GetMapping("/sync/status")
    public Result<SyncStatusResponseDTO> getSyncStatus() {
        try {
            Map<String, Object> syncStatus = systemSettingsService.getKnowledgeSyncStatus();

            SyncStatusResponseDTO responseDTO = SyncStatusResponseDTO.builder()
                    .syncStatus((Integer) syncStatus.get("syncStatus"))
                    .syncMessage((String) syncStatus.get("syncMessage"))
                    .lastSyncTime((LocalDateTime) syncStatus.get("lastSyncTime"))
                    .build();

            return Result.success(responseDTO);
        } catch (Exception e) {
            log.error("获取同步状态失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取同步状态失败: " + e.getMessage());
        }
    }
}
