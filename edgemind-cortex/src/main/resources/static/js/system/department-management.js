/**
 * 部门管理页面JavaScript
 */

// 全局变量
let deptTree = [];
let allDepts = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadDeptTree();
    loadUsers();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 可以添加其他事件绑定
}

/**
 * 加载部门树
 */
function loadDeptTree() {
    fetch('/wkg/api/system/dept/tree')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                deptTree = data.data;
                renderDeptTree(deptTree);
                loadAllDepts();
            } else {
                showAlert('加载部门树失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 加载所有部门（用于父部门选择）
 */
function loadAllDepts() {
    fetch('/wkg/api/system/dept/all')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allDepts = data.data;
                updateParentDeptOptions();
            }
        })
        .catch(error => {
            console.error('Error loading all departments:', error);
        });
}

/**
 * 加载用户列表（用于负责人选择）
 */
function loadUsers() {
    fetch('/wkg/api/system/user/all')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateManagerOptions(data.data);
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
        });
}

/**
 * 渲染部门树
 */
function renderDeptTree(nodes, level = 0) {
    const container = document.getElementById('deptTreeContainer');
    if (level === 0) {
        container.innerHTML = '';
    }
    
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `dept-node level-${level}`;
        div.setAttribute('data-dept-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center flex-grow-1" onclick="selectDept(${node.id})">
                    ${expandIcon}
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-building me-2"></i>
                            <strong>${node.deptName}</strong>
                            <span class="badge bg-secondary ms-2">${node.deptCode}</span>
                            ${node.status === 0 ? '<span class="badge bg-danger ms-2">禁用</span>' : ''}
                        </div>
                        <div class="dept-stats mt-1">
                            <small>
                                <i class="bi bi-people me-1"></i>员工: ${node.userCount || 0}
                                ${node.managerName ? `<i class="bi bi-person-badge ms-2 me-1"></i>负责人: ${node.managerName}` : ''}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editDept(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="addChildDept(${node.id})" title="添加子部门">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDept(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(div);
        
        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'dept-children';
            childContainer.style.display = level === 0 ? 'block' : 'none'; // 默认展开第一层
            container.appendChild(childContainer);
            
            // 递归渲染子节点
            renderDeptTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 渲染部门树子节点
 */
function renderDeptTreeChildren(nodes, container, level) {
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `dept-node level-${level}`;
        div.setAttribute('data-dept-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center flex-grow-1" onclick="selectDept(${node.id})">
                    ${expandIcon}
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-building me-2"></i>
                            <strong>${node.deptName}</strong>
                            <span class="badge bg-secondary ms-2">${node.deptCode}</span>
                            ${node.status === 0 ? '<span class="badge bg-danger ms-2">禁用</span>' : ''}
                        </div>
                        <div class="dept-stats mt-1">
                            <small>
                                <i class="bi bi-people me-1"></i>员工: ${node.userCount || 0}
                                ${node.managerName ? `<i class="bi bi-person-badge ms-2 me-1"></i>负责人: ${node.managerName}` : ''}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editDept(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="addChildDept(${node.id})" title="添加子部门">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDept(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(div);
        
        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'dept-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);
            
            // 递归渲染子节点
            renderDeptTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const childContainer = icon.closest('.dept-node').nextElementSibling;
    
    if (isExpanded) {
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
        if (childContainer && childContainer.classList.contains('dept-children')) {
            childContainer.style.display = 'none';
        }
    } else {
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
        if (childContainer && childContainer.classList.contains('dept-children')) {
            childContainer.style.display = 'block';
        }
    }
}

/**
 * 展开全部节点
 */
function expandAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-right');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 收起全部节点
 */
function collapseAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-down');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 选择部门
 */
function selectDept(deptId) {
    // 移除之前的选中状态
    document.querySelectorAll('.dept-node').forEach(node => {
        node.classList.remove('selected');
    });
    
    // 添加选中状态
    const selectedNode = document.querySelector(`[data-dept-id="${deptId}"]`);
    if (selectedNode) {
        selectedNode.classList.add('selected');
    }
    
    // 加载部门详情
    loadDeptDetail(deptId);
}

/**
 * 加载部门详情
 */
function loadDeptDetail(deptId) {
    fetch(`/wkg/api/system/dept/${deptId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderDeptDetail(data.data);
            } else {
                showAlert('加载部门详情失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 渲染部门详情
 */
function renderDeptDetail(dept) {
    const detailContainer = document.getElementById('deptDetail');
    detailContainer.innerHTML = `
        <div class="mb-3">
            <label class="form-label fw-bold">部门名称</label>
            <div class="form-control-plaintext">${dept.deptName}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门编码</label>
            <div class="form-control-plaintext"><code>${dept.deptCode}</code></div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门路径</label>
            <div class="form-control-plaintext">${dept.deptPath || '-'}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门层级</label>
            <div class="form-control-plaintext">第 ${dept.deptLevel} 级</div>
        </div>
        ${dept.managerName ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门负责人</label>
            <div class="form-control-plaintext">${dept.managerName}</div>
        </div>
        ` : ''}
        ${dept.phone ? `
        <div class="mb-3">
            <label class="form-label fw-bold">联系电话</label>
            <div class="form-control-plaintext">${dept.phone}</div>
        </div>
        ` : ''}
        ${dept.email ? `
        <div class="mb-3">
            <label class="form-label fw-bold">邮箱</label>
            <div class="form-control-plaintext">${dept.email}</div>
        </div>
        ` : ''}
        ${dept.address ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门地址</label>
            <div class="form-control-plaintext">${dept.address}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">状态</label>
            <div class="form-control-plaintext">
                <span class="badge ${dept.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${dept.status === 1 ? '启用' : '禁用'}
                </span>
            </div>
        </div>
        ${dept.description ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门描述</label>
            <div class="form-control-plaintext">${dept.description}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">创建时间</label>
            <div class="form-control-plaintext">${formatDateTime(dept.createTime)}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">更新时间</label>
            <div class="form-control-plaintext">${formatDateTime(dept.updateTime)}</div>
        </div>
    `;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 这里可以使用Toast或其他提示组件
    alert(message);
}

/**
 * 显示创建部门模态框
 */
function showCreateModal() {
    document.getElementById('deptModalTitle').textContent = '新增部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('parentId').value = '0';

    const modal = new bootstrap.Modal(document.getElementById('deptModal'));
    modal.show();
}

/**
 * 添加子部门
 */
function addChildDept(parentId) {
    document.getElementById('deptModalTitle').textContent = '新增子部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('parentId').value = parentId;

    const modal = new bootstrap.Modal(document.getElementById('deptModal'));
    modal.show();
}

/**
 * 编辑部门
 */
function editDept(deptId) {
    fetch(`/wkg/api/system/dept/${deptId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dept = data.data;
                document.getElementById('deptModalTitle').textContent = '编辑部门';
                document.getElementById('deptId').value = dept.id;
                document.getElementById('deptName').value = dept.deptName;
                document.getElementById('deptCode').value = dept.deptCode;
                document.getElementById('parentId').value = dept.parentId || '0';
                document.getElementById('managerId').value = dept.managerId || '';
                document.getElementById('phone').value = dept.phone || '';
                document.getElementById('email').value = dept.email || '';
                document.getElementById('sortOrder').value = dept.sortOrder || 0;
                document.getElementById('status').value = dept.status;
                document.getElementById('address').value = dept.address || '';
                document.getElementById('description').value = dept.description || '';

                const modal = new bootstrap.Modal(document.getElementById('deptModal'));
                modal.show();
            } else {
                showAlert('获取部门信息失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 保存部门
 */
function saveDept() {
    const deptId = document.getElementById('deptId').value;
    const isEdit = !!deptId;

    // 表单验证
    if (!validateDeptForm()) {
        return;
    }

    const formData = {
        deptName: document.getElementById('deptName').value,
        deptCode: document.getElementById('deptCode').value,
        parentId: parseInt(document.getElementById('parentId').value) || 0,
        managerId: parseInt(document.getElementById('managerId').value) || null,
        phone: document.getElementById('phone').value || null,
        email: document.getElementById('email').value || null,
        sortOrder: parseInt(document.getElementById('sortOrder').value) || 0,
        status: parseInt(document.getElementById('status').value),
        address: document.getElementById('address').value || null,
        description: document.getElementById('description').value || null
    };

    if (isEdit) {
        formData.id = parseInt(deptId);
    }

    const url = isEdit ? `/wkg/api/system/dept/${deptId}` : '/wkg/api/system/dept';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(isEdit ? '部门更新成功' : '部门创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('deptModal')).hide();
            loadDeptTree();
        } else {
            showAlert((isEdit ? '部门更新失败: ' : '部门创建失败: ') + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 删除部门
 */
function deleteDept(deptId) {
    // 先检查是否可以删除
    fetch(`/wkg/api/system/dept/${deptId}/can-delete`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (!data.data) {
                    showAlert('该部门下还有子部门或用户，无法删除', 'warning');
                    return;
                }

                if (!confirm('确定要删除这个部门吗？此操作不可恢复。')) {
                    return;
                }

                fetch(`/wkg/api/system/dept/${deptId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('部门删除成功', 'success');
                        loadDeptTree();
                        // 清空详情面板
                        document.getElementById('deptDetail').innerHTML = `
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-building" style="font-size: 3rem;"></i>
                                <p class="mt-2">请选择一个部门查看详情</p>
                            </div>
                        `;
                    } else {
                        showAlert('部门删除失败: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('网络错误，请稍后重试', 'danger');
                });
            } else {
                showAlert('检查部门状态失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 更新父部门选项
 */
function updateParentDeptOptions() {
    const parentSelect = document.getElementById('parentId');
    const currentOptions = parentSelect.innerHTML;

    // 保留顶级部门选项
    parentSelect.innerHTML = '<option value="0">顶级部门</option>';

    // 添加其他部门作为父部门选项
    allDepts.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept.id;
        option.textContent = dept.deptName;
        parentSelect.appendChild(option);
    });
}

/**
 * 更新负责人选项
 */
function updateManagerOptions(users) {
    const managerSelect = document.getElementById('managerId');
    managerSelect.innerHTML = '<option value="">请选择负责人</option>';

    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.nickname || user.username} (${user.username})`;
        managerSelect.appendChild(option);
    });
}

/**
 * 表单验证
 */
function validateDeptForm() {
    const deptName = document.getElementById('deptName').value.trim();
    const deptCode = document.getElementById('deptCode').value.trim();
    const email = document.getElementById('email').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!deptName) {
        showAlert('请输入部门名称', 'warning');
        return false;
    }

    if (!deptCode) {
        showAlert('请输入部门编码', 'warning');
        return false;
    }

    // 部门编码格式验证
    if (!/^[A-Z0-9_]+$/.test(deptCode)) {
        showAlert('部门编码只能包含大写字母、数字和下划线', 'warning');
        return false;
    }

    // 邮箱格式验证
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        showAlert('邮箱格式不正确', 'warning');
        return false;
    }

    // 手机号格式验证
    if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        showAlert('手机号格式不正确', 'warning');
        return false;
    }

    return true;
}
