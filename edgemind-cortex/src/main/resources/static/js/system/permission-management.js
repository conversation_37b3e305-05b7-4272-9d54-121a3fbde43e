/**
 * 权限管理页面JavaScript
 */

// 全局变量
let permissionTree = [];
let allPermissions = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadPermissionTree();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 权限类型变化事件
    document.getElementById('permissionType').addEventListener('change', function() {
        const type = this.value;
        const resourceUrlGroup = document.getElementById('resourceUrl').parentElement;
        const httpMethodGroup = document.getElementById('httpMethod').parentElement;
        
        if (type === 'API') {
            resourceUrlGroup.style.display = 'block';
            httpMethodGroup.style.display = 'block';
            document.getElementById('resourceUrl').required = true;
        } else {
            resourceUrlGroup.style.display = 'none';
            httpMethodGroup.style.display = 'none';
            document.getElementById('resourceUrl').required = false;
        }
    });
}

/**
 * 加载权限树
 */
function loadPermissionTree() {
    fetch('/wkg/api/system/permission/tree')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                permissionTree = data.data;
                renderPermissionTree(permissionTree);
                loadAllPermissions();
            } else {
                showAlert('加载权限树失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 加载所有权限（用于父权限选择）
 */
function loadAllPermissions() {
    fetch('/wkg/api/system/permission/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allPermissions = data.data;
                updateParentPermissionOptions();
            }
        })
        .catch(error => {
            console.error('Error loading all permissions:', error);
        });
}

/**
 * 渲染权限树
 */
function renderPermissionTree(nodes, level = 0) {
    const container = document.getElementById('permissionTreeContainer');
    if (level === 0) {
        container.innerHTML = '';
    }
    
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `permission-node level-${level}`;
        div.setAttribute('data-permission-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center" onclick="selectPermission(${node.id})">
                    ${expandIcon}
                    ${node.icon ? `<i class="${node.icon} me-2"></i>` : ''}
                    <strong>${node.permissionName}</strong>
                    <span class="badge permission-type-badge ${getPermissionTypeBadgeClass(node.type)} ms-2">
                        ${getPermissionTypeText(node.type)}
                    </span>
                    <small class="text-muted ms-2">${node.permissionCode}</small>
                </div>
                <div class="permission-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editPermission(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="addChildPermission(${node.id})" title="添加子权限">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deletePermission(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(div);
        
        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'permission-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);
            
            // 递归渲染子节点
            renderPermissionTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 渲染权限树子节点
 */
function renderPermissionTreeChildren(nodes, container, level) {
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `permission-node level-${level}`;
        div.setAttribute('data-permission-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center" onclick="selectPermission(${node.id})">
                    ${expandIcon}
                    ${node.icon ? `<i class="${node.icon} me-2"></i>` : ''}
                    <strong>${node.permissionName}</strong>
                    <span class="badge permission-type-badge ${getPermissionTypeBadgeClass(node.type)} ms-2">
                        ${getPermissionTypeText(node.type)}
                    </span>
                    <small class="text-muted ms-2">${node.permissionCode}</small>
                </div>
                <div class="permission-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editPermission(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="addChildPermission(${node.id})" title="添加子权限">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deletePermission(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(div);
        
        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'permission-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);
            
            // 递归渲染子节点
            renderPermissionTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const childContainer = icon.closest('.permission-node').nextElementSibling;
    
    if (isExpanded) {
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
        if (childContainer && childContainer.classList.contains('permission-children')) {
            childContainer.style.display = 'none';
        }
    } else {
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
        if (childContainer && childContainer.classList.contains('permission-children')) {
            childContainer.style.display = 'block';
        }
    }
}

/**
 * 展开全部节点
 */
function expandAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-right');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 收起全部节点
 */
function collapseAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-down');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 选择权限
 */
function selectPermission(permissionId) {
    // 移除之前的选中状态
    document.querySelectorAll('.permission-node').forEach(node => {
        node.classList.remove('border-primary', 'bg-primary', 'bg-opacity-10');
    });
    
    // 添加选中状态
    const selectedNode = document.querySelector(`[data-permission-id="${permissionId}"]`);
    if (selectedNode) {
        selectedNode.classList.add('border-primary', 'bg-primary', 'bg-opacity-10');
    }
    
    // 加载权限详情
    loadPermissionDetail(permissionId);
}

/**
 * 加载权限详情
 */
function loadPermissionDetail(permissionId) {
    fetch(`/wkg/api/system/permission/${permissionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderPermissionDetail(data.data);
            } else {
                showAlert('加载权限详情失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 渲染权限详情
 */
function renderPermissionDetail(permission) {
    const detailContainer = document.getElementById('permissionDetail');
    detailContainer.innerHTML = `
        <div class="mb-3">
            <label class="form-label fw-bold">权限名称</label>
            <div class="form-control-plaintext">${permission.permissionName}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">权限编码</label>
            <div class="form-control-plaintext"><code>${permission.permissionCode}</code></div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">权限类型</label>
            <div class="form-control-plaintext">
                <span class="badge ${getPermissionTypeBadgeClass(permission.type)}">
                    ${getPermissionTypeText(permission.type)}
                </span>
            </div>
        </div>
        ${permission.resourceUrl ? `
        <div class="mb-3">
            <label class="form-label fw-bold">资源URL</label>
            <div class="form-control-plaintext"><code>${permission.resourceUrl}</code></div>
        </div>
        ` : ''}
        ${permission.httpMethod ? `
        <div class="mb-3">
            <label class="form-label fw-bold">HTTP方法</label>
            <div class="form-control-plaintext"><span class="badge bg-info">${permission.httpMethod}</span></div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">状态</label>
            <div class="form-control-plaintext">
                <span class="badge ${permission.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${permission.status === 1 ? '启用' : '禁用'}
                </span>
            </div>
        </div>
        ${permission.description ? `
        <div class="mb-3">
            <label class="form-label fw-bold">描述</label>
            <div class="form-control-plaintext">${permission.description}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">创建时间</label>
            <div class="form-control-plaintext">${formatDateTime(permission.createTime)}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">更新时间</label>
            <div class="form-control-plaintext">${formatDateTime(permission.updateTime)}</div>
        </div>
    `;
}

/**
 * 获取权限类型对应的徽章样式
 */
function getPermissionTypeBadgeClass(type) {
    const typeMap = {
        'MENU': 'bg-primary',
        'API': 'bg-success',
        'BUTTON': 'bg-warning',
        'DATA': 'bg-info'
    };
    return typeMap[type] || 'bg-secondary';
}

/**
 * 获取权限类型文本
 */
function getPermissionTypeText(type) {
    const typeMap = {
        'MENU': '菜单',
        'API': '接口',
        'BUTTON': '按钮',
        'DATA': '数据'
    };
    return typeMap[type] || type;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 这里可以使用Toast或其他提示组件
    alert(message);
}

/**
 * 显示创建权限模态框
 */
function showCreateModal() {
    document.getElementById('permissionModalTitle').textContent = '新增权限';
    document.getElementById('permissionForm').reset();
    document.getElementById('permissionId').value = '';
    document.getElementById('parentId').value = '0';

    const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
    modal.show();
}

/**
 * 添加子权限
 */
function addChildPermission(parentId) {
    document.getElementById('permissionModalTitle').textContent = '新增子权限';
    document.getElementById('permissionForm').reset();
    document.getElementById('permissionId').value = '';
    document.getElementById('parentId').value = parentId;

    const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
    modal.show();
}

/**
 * 编辑权限
 */
function editPermission(permissionId) {
    fetch(`/wkg/api/system/permission/${permissionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const permission = data.data;
                document.getElementById('permissionModalTitle').textContent = '编辑权限';
                document.getElementById('permissionId').value = permission.id;
                document.getElementById('permissionName').value = permission.permissionName;
                document.getElementById('permissionCode').value = permission.permissionCode;
                document.getElementById('permissionType').value = permission.type;
                document.getElementById('parentId').value = permission.parentId || '0';
                document.getElementById('resourceUrl').value = permission.resourceUrl || '';
                document.getElementById('httpMethod').value = permission.httpMethod || '';
                document.getElementById('icon').value = permission.icon || '';
                document.getElementById('sortOrder').value = permission.sortOrder || 0;
                document.getElementById('status').value = permission.status;
                document.getElementById('description').value = permission.description || '';

                // 触发类型变化事件
                document.getElementById('permissionType').dispatchEvent(new Event('change'));

                const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
                modal.show();
            } else {
                showAlert('获取权限信息失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 保存权限
 */
function savePermission() {
    const permissionId = document.getElementById('permissionId').value;
    const isEdit = !!permissionId;

    // 表单验证
    if (!validatePermissionForm()) {
        return;
    }

    const formData = {
        permissionName: document.getElementById('permissionName').value,
        permissionCode: document.getElementById('permissionCode').value,
        type: document.getElementById('permissionType').value,
        parentId: parseInt(document.getElementById('parentId').value) || 0,
        resourceUrl: document.getElementById('resourceUrl').value || null,
        httpMethod: document.getElementById('httpMethod').value || null,
        icon: document.getElementById('icon').value || null,
        sortOrder: parseInt(document.getElementById('sortOrder').value) || 0,
        status: parseInt(document.getElementById('status').value),
        description: document.getElementById('description').value || null
    };

    if (isEdit) {
        formData.id = parseInt(permissionId);
    }

    const url = isEdit ? `/wkg/api/system/permission/${permissionId}` : '/wkg/api/system/permission';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(isEdit ? '权限更新成功' : '权限创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
            loadPermissionTree();
        } else {
            showAlert((isEdit ? '权限更新失败: ' : '权限创建失败: ') + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 删除权限
 */
function deletePermission(permissionId) {
    if (!confirm('确定要删除这个权限吗？此操作不可恢复，且会影响相关的角色权限。')) {
        return;
    }

    fetch(`/wkg/api/system/permission/${permissionId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('权限删除成功', 'success');
            loadPermissionTree();
            // 清空详情面板
            document.getElementById('permissionDetail').innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="bi bi-cursor-text" style="font-size: 3rem;"></i>
                    <p class="mt-2">请选择一个权限查看详情</p>
                </div>
            `;
        } else {
            showAlert('权限删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 更新父权限选项
 */
function updateParentPermissionOptions() {
    const parentSelect = document.getElementById('parentId');
    const currentOptions = parentSelect.innerHTML;

    // 保留顶级权限选项
    parentSelect.innerHTML = '<option value="0">顶级权限</option>';

    // 添加菜单类型的权限作为父权限选项
    allPermissions.filter(p => p.type === 'MENU').forEach(permission => {
        const option = document.createElement('option');
        option.value = permission.id;
        option.textContent = permission.permissionName;
        parentSelect.appendChild(option);
    });
}

/**
 * 表单验证
 */
function validatePermissionForm() {
    const permissionName = document.getElementById('permissionName').value.trim();
    const permissionCode = document.getElementById('permissionCode').value.trim();
    const permissionType = document.getElementById('permissionType').value;
    const resourceUrl = document.getElementById('resourceUrl').value.trim();

    if (!permissionName) {
        showAlert('请输入权限名称', 'warning');
        return false;
    }

    if (!permissionCode) {
        showAlert('请输入权限编码', 'warning');
        return false;
    }

    if (!permissionType) {
        showAlert('请选择权限类型', 'warning');
        return false;
    }

    // 权限编码格式验证
    if (!/^[a-zA-Z0-9:_-]+$/.test(permissionCode)) {
        showAlert('权限编码只能包含字母、数字、冒号、下划线和横线', 'warning');
        return false;
    }

    // API权限必须填写资源URL
    if (permissionType === 'API' && !resourceUrl) {
        showAlert('API权限必须填写资源URL', 'warning');
        return false;
    }

    return true;
}
