-- RBAC权限修复脚本
-- 确保管理员用户拥有所有必要的权限

-- 1. 检查并添加缺失的权限（如果需要）
-- 注意：数据库中已经有了正确的权限结构，这里只是确保一致性

-- 2. 确保超级管理员角色拥有所有权限
-- 删除现有的角色权限关联（超级管理员角色ID=1）
DELETE FROM sys_role_permission WHERE role_id = 1;

-- 重新分配所有权限给超级管理员角色
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 1, id FROM sys_permission WHERE status = 1;

-- 3. 确保管理员用户拥有超级管理员角色
-- 检查用户角色关联是否存在
INSERT IGNORE INTO sys_user_role (user_id, role_id) VALUES (1, 1);

-- 4. 验证权限分配
-- 查询管理员用户的权限
SELECT 
    u.username,
    r.role_name,
    p.permission_name,
    p.permission_code
FROM sys_user u
JOIN sys_user_role ur ON u.id = ur.user_id
JOIN sys_role r ON ur.role_id = r.id
JOIN sys_role_permission rp ON r.id = rp.role_id
JOIN sys_permission p ON rp.permission_id = p.id
WHERE u.username = 'admin'
ORDER BY p.permission_code;

-- 5. 显示权限管理相关的权限
SELECT 
    id,
    permission_name,
    permission_code,
    type,
    parent_id
FROM sys_permission 
WHERE permission_code LIKE 'menu:view:%' 
   OR permission_code LIKE 'menu:system%'
ORDER BY parent_id, id;
