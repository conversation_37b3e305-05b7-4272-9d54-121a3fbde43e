<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置</title>
    <!-- 引入 Bootstrap 和其他通用样式 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css" />
    <link rel="stylesheet" href="/wkg/css/components/scrollbar-styles.css" />
    <link rel="stylesheet" href="/wkg/css/features/settings/settings.css" />
</head>
<body>
    <div class="settings-container">
        <div class="page-header">
            <h1>系统设置</h1>
        </div>

        <!-- 系统信息区块 -->
        <div class="settings-section">
            <div class="settings-card">
                <div class="card-header">
                    <h5 class="card-title">系统信息</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-3">
                            <span class="fw-medium text-secondary">系统版本</span>
                        </div>
                        <div class="col-9">
                            <span id="systemVersion" class="text-primary fw-medium">1.0.0</span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-3">
                            <span class="fw-medium text-secondary">系统标识</span>
                        </div>
                        <div class="col-9">
                            <div class="d-flex align-items-center">
                                <span id="systemIdentifier" class="me-2 text-primary fw-medium"></span>
                                <button id="copyIdentifierBtn" class="btn btn-sm btn-outline-secondary" title="复制标识码">
                                    <i class="bi bi-clipboard"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库设置区块 -->
        <div class="settings-section">
            <div class="settings-card">
                <div class="card-header">
                    <h5 class="card-title">知识库设置</h5>
                </div>
                <div class="card-body">
                    <!-- 使用说明 -->
                    <div class="alert alert-info">
                        <h6 class="alert-heading">功能说明</h6>
                        <p class="mb-2">设置知识库存储路径，点击同步即可将本地文档一键同步到端智AI助手。</p>
                        <p class="mb-0">支持格式：Word(.doc, .docx)、PDF(.pdf)、Markdown(.md)、文本(.txt)</p>
                    </div>

                    <form id="knowledgeBaseSettingsForm">
                        <div class="mb-3">
                            <label for="storagePath" class="form-label settings-form-label">知识库存储路径</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="storagePath" placeholder="请输入知识库文件存储路径" autocomplete="off">
                                <button class="btn btn-primary" type="button" id="syncButton">
                                    <i class="bi bi-arrow-repeat me-1"></i> 同步
                                </button>
                            </div>
                            <div class="form-text">指定知识库文件的存储根目录</div>
                        </div>

                        <div class="mb-3">
                            <label for="personalStoragePath" class="form-label settings-form-label">个人库存储路径</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="personalStoragePath" placeholder="请输入个人库文件存储路径" autocomplete="off">
                                <button class="btn btn-outline-secondary" type="button" id="savePersonalPathButton">
                                    <i class="bi bi-save me-1"></i> 保存
                                </button>
                            </div>
                            <div class="form-text">指定个人库文件的存储根目录</div>
                        </div>

                        <!-- 同步状态信息 -->
                        <div class="sync-status-section">
                            <h6 class="section-title">同步状态</h6>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <span class="fw-medium text-secondary">当前状态</span>
                                </div>
                                <div class="col-9">
                                    <span id="syncStatus" class="text-primary fw-medium">未同步</span>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-3">
                                    <span class="fw-medium text-secondary">最后同步时间</span>
                                </div>
                                <div class="col-9">
                                    <span id="lastSyncTime" class="text-primary fw-medium">无</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 同步确认模态框 -->
        <div class="modal fade" id="syncConfirmModal" tabindex="-1" aria-labelledby="syncConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="syncConfirmModalLabel">确认同步操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 2rem;"></i>
                        </div>
                        <div class="alert alert-warning">
                            <h6 class="alert-heading">数据同步风险提示</h6>
                            <p class="mb-2">此操作将执行以下操作：</p>
                            <ol class="mb-0">
                                <li>保存您输入的知识库存储路径</li>
                                <li>清除当前知识库中的所有数据</li>
                                <li>扫描指定目录下的所有文件和文件夹结构</li>
                                <li>将扫描到的文件重新导入到知识库中</li>
                            </ol>
                        </div>
                        <div class="alert alert-info">
                            <p class="mb-1"><i class="bi bi-info-circle-fill me-2"></i>建议在执行此操作前备份重要数据</p>
                            <p class="mb-0"><i class="bi bi-exclamation-circle-fill me-2"></i>注意：系统仅同步符合命名规范的文件和目录</p>
                        </div>
                        <p class="text-center fw-medium">确定要继续此操作吗？</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmSyncBtn">确认同步</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- 引入 Bootstrap JS 和其他脚本 -->
    <script src="/wkg/js/vendor/jquery-3.7.1.min.js"></script>
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script src="/wkg/js/features/settings/settings.js"></script>

    <!-- 添加Toast容器 -->
    <div th:replace="fragments/_toastContainer :: toastContainer"></div>
</body>
</html>
