<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 端智AI助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .search-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .btn-group-actions {
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .operation-type-badge {
            font-size: 0.75rem;
        }
        .log-detail {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .statistics-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-journal-text me-2"></i>操作日志</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">系统管理</a></li>
                    <li class="breadcrumb-item active">操作日志</li>
                </ol>
            </nav>
        </div>

        <!-- 统计卡片 -->
        <div class="statistics-card">
            <div class="row" id="statisticsRow">
                <div class="col-md-3 stat-item">
                    <div class="stat-number" id="totalOperations">-</div>
                    <div class="stat-label">总操作数</div>
                </div>
                <div class="col-md-3 stat-item">
                    <div class="stat-number" id="todayOperations">-</div>
                    <div class="stat-label">今日操作</div>
                </div>
                <div class="col-md-3 stat-item">
                    <div class="stat-number" id="successRate">-</div>
                    <div class="stat-label">成功率</div>
                </div>
                <div class="col-md-3 stat-item">
                    <div class="stat-number" id="activeUsers">-</div>
                    <div class="stat-label">活跃用户</div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" id="searchUsername" placeholder="请输入用户名">
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作类型</label>
                    <select class="form-select" id="searchOperationType">
                        <option value="">全部类型</option>
                        <option value="CREATE">新增</option>
                        <option value="UPDATE">修改</option>
                        <option value="DELETE">删除</option>
                        <option value="LOGIN">登录</option>
                        <option value="LOGOUT">登出</option>
                        <option value="QUERY">查询</option>
                        <option value="EXPORT">导出</option>
                        <option value="IMPORT">导入</option>
                        <option value="RESET_PASSWORD">重置密码</option>
                        <option value="ASSIGN_ROLE">分配角色</option>
                        <option value="GRANT_PERMISSION">授权</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作模块</label>
                    <input type="text" class="form-control" id="searchModule" placeholder="请输入模块名称">
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作状态</label>
                    <select class="form-select" id="searchStatus">
                        <option value="">全部状态</option>
                        <option value="1">成功</option>
                        <option value="0">失败</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">开始时间</label>
                    <input type="datetime-local" class="form-control" id="searchStartTime">
                </div>
                <div class="col-md-6">
                    <label class="form-label">结束时间</label>
                    <input type="datetime-local" class="form-control" id="searchEndTime">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>搜索
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="resetSearch()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="setQuickTimeRange('today')">
                        <i class="bi bi-calendar-day me-1"></i>今天
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="setQuickTimeRange('week')">
                        <i class="bi bi-calendar-week me-1"></i>本周
                    </button>
                    <button type="button" class="btn btn-info ms-2" onclick="setQuickTimeRange('month')">
                        <i class="bi bi-calendar-month me-1"></i>本月
                    </button>
                </div>
            </form>
        </div>

        <!-- 操作按钮组 -->
        <div class="btn-group-actions">
            <button type="button" class="btn btn-success" onclick="exportLogs()">
                <i class="bi bi-download me-1"></i>导出日志
            </button>
            <button type="button" class="btn btn-warning" onclick="showCleanModal()">
                <i class="bi bi-trash3 me-1"></i>清理日志
            </button>
            <button type="button" class="btn btn-info" onclick="refreshStatistics()">
                <i class="bi bi-arrow-clockwise me-1"></i>刷新统计
            </button>
        </div>

        <!-- 日志列表表格 -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>操作时间</th>
                            <th>用户</th>
                            <th>操作类型</th>
                            <th>模块</th>
                            <th>操作描述</th>
                            <th>IP地址</th>
                            <th>执行时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="logTableBody">
                        <!-- 日志数据将通过JS动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="日志列表分页">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JS动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">操作日志详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>操作用户：</strong>
                            <span id="detailUsername"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>操作时间：</strong>
                            <span id="detailCreateTime"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>操作类型：</strong>
                            <span id="detailOperationType"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>操作模块：</strong>
                            <span id="detailModule"></span>
                        </div>
                        <div class="col-12">
                            <strong>操作描述：</strong>
                            <span id="detailDescription"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>请求URL：</strong>
                            <span id="detailRequestUrl"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>请求方法：</strong>
                            <span id="detailRequestMethod"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>IP地址：</strong>
                            <span id="detailIpAddress"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>执行时间：</strong>
                            <span id="detailExecutionTime"></span>
                        </div>
                        <div class="col-12">
                            <strong>用户代理：</strong>
                            <div id="detailUserAgent" class="text-break"></div>
                        </div>
                        <div class="col-12" id="requestParamsSection">
                            <strong>请求参数：</strong>
                            <pre id="detailRequestParams" class="bg-light p-2 rounded mt-1"></pre>
                        </div>
                        <div class="col-12" id="responseResultSection">
                            <strong>响应结果：</strong>
                            <pre id="detailResponseResult" class="bg-light p-2 rounded mt-1"></pre>
                        </div>
                        <div class="col-12" id="errorMessageSection">
                            <strong>错误信息：</strong>
                            <div id="detailErrorMessage" class="text-danger"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 清理日志模态框 -->
    <div class="modal fade" id="cleanLogModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">清理操作日志</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        此操作将删除指定天数之前的所有操作日志，删除后无法恢复！
                    </div>
                    <div class="mb-3">
                        <label class="form-label">保留天数 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="retentionDays" value="90" min="1" max="365">
                        <div class="form-text">将删除 <span id="retentionDays">90</span> 天之前的日志记录</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="cleanLogs()">确认清理</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/wkg/static/js/system/operation-log.js"></script>
</body>
</html>
