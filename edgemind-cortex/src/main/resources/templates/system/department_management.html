<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理 - 端智AI助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .dept-tree {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        .dept-node {
            margin: 8px 0;
            padding: 12px;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            background: #f8f9fa;
            transition: all 0.2s;
        }
        .dept-node:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .dept-node.selected {
            background: #e3f2fd;
            border-color: #2196f3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
        }
        .dept-node.level-0 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }
        .dept-node.level-1 {
            margin-left: 20px;
            background: #f3e5f5;
            border-color: #9c27b0;
        }
        .dept-node.level-2 {
            margin-left: 40px;
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .dept-node.level-3 {
            margin-left: 60px;
            background: #fff3e0;
            border-color: #ff9800;
        }
        .dept-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }
        .dept-node:hover .dept-actions {
            opacity: 1;
        }
        .dept-stats {
            font-size: 0.85rem;
            color: #6c757d;
        }
        .dept-node.level-0 .dept-stats {
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-diagram-3-fill me-2"></i>部门管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">系统管理</a></li>
                    <li class="breadcrumb-item active">部门管理</li>
                </ol>
            </nav>
        </div>

        <div class="row">
            <!-- 部门树 -->
            <div class="col-md-8">
                <div class="dept-tree">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="bi bi-diagram-3 me-2"></i>组织架构</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                <i class="bi bi-arrows-expand"></i> 展开全部
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                                <i class="bi bi-arrows-collapse"></i> 收起全部
                            </button>
                            <button type="button" class="btn btn-sm btn-success" onclick="showCreateModal()">
                                <i class="bi bi-plus-lg"></i> 新增部门
                            </button>
                        </div>
                    </div>
                    <div id="deptTreeContainer">
                        <!-- 部门树将通过JS动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 部门详情 -->
            <div class="col-md-4">
                <div class="table-container">
                    <h5><i class="bi bi-info-circle me-2"></i>部门详情</h5>
                    <div id="deptDetail">
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-building" style="font-size: 3rem;"></i>
                            <p class="mt-2">请选择一个部门查看详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑部门模态框 -->
    <div class="modal fade" id="deptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deptModalTitle">新增部门</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="deptForm">
                        <input type="hidden" id="deptId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">部门名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="deptName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">部门编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="deptCode" required>
                                    <div class="form-text">部门编码必须唯一</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">上级部门</label>
                                    <select class="form-select" id="parentId">
                                        <option value="0">顶级部门</option>
                                        <!-- 父部门选项将通过JS动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">部门负责人</label>
                                    <select class="form-select" id="managerId">
                                        <option value="">请选择负责人</option>
                                        <!-- 用户选项将通过JS动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">联系电话</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">排序</label>
                                    <input type="number" class="form-control" id="sortOrder" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="status">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">部门地址</label>
                                    <input type="text" class="form-control" id="address">
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">部门描述</label>
                                    <textarea class="form-control" id="description" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveDept()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/wkg/static/js/system/department-management.js"></script>
</body>
</html>
