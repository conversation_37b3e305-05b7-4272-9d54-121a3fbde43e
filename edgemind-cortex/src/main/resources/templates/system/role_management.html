<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 端智AI助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .search-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .btn-group-actions {
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .permission-tree {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
        }
        .permission-node {
            margin: 5px 0;
        }
        .permission-node .form-check {
            margin-left: 20px;
        }
        .permission-node.level-0 .form-check {
            margin-left: 0;
        }
        .permission-node.level-1 .form-check {
            margin-left: 20px;
        }
        .permission-node.level-2 .form-check {
            margin-left: 40px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-people-fill me-2"></i>角色管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">系统管理</a></li>
                    <li class="breadcrumb-item active">角色管理</li>
                </ol>
            </nav>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">角色名称</label>
                    <input type="text" class="form-control" id="searchRoleName" placeholder="请输入角色名称">
                </div>
                <div class="col-md-4">
                    <label class="form-label">角色编码</label>
                    <input type="text" class="form-control" id="searchRoleCode" placeholder="请输入角色编码">
                </div>
                <div class="col-md-4">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="searchStatus">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>搜索
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="resetSearch()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                    </button>
                </div>
            </form>
        </div>

        <!-- 操作按钮组 -->
        <div class="btn-group-actions">
            <button type="button" class="btn btn-success" onclick="showCreateModal()">
                <i class="bi bi-plus-lg me-1"></i>新增角色
            </button>
            <button type="button" class="btn btn-danger" onclick="batchDelete()" disabled id="batchDeleteBtn">
                <i class="bi bi-trash me-1"></i>批量删除
            </button>
        </div>

        <!-- 角色列表表格 -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>角色名称</th>
                            <th>角色编码</th>
                            <th>描述</th>
                            <th>状态</th>
                            <th>用户数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="roleTableBody">
                        <!-- 角色数据将通过JS动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="角色列表分页">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JS动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 创建/编辑角色模态框 -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="roleModalTitle">新增角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <input type="hidden" id="roleId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">角色名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="roleName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">角色编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="roleCode" required>
                                    <div class="form-text">角色编码只能包含大写字母和下划线</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="status">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">描述</label>
                                    <textarea class="form-control" id="description" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限分配模态框 -->
    <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">权限分配</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="permissionRoleId">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label">权限列表</label>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                    <i class="bi bi-arrows-expand"></i> 展开全部
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                                    <i class="bi bi-arrows-collapse"></i> 收起全部
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="checkAll()">
                                    <i class="bi bi-check-all"></i> 全选
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-warning" onclick="uncheckAll()">
                                    <i class="bi bi-x-square"></i> 取消全选
                                </button>
                            </div>
                        </div>
                        <div class="permission-tree" id="permissionTree">
                            <!-- 权限树将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePermissions()">保存权限</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 复制角色模态框 -->
    <div class="modal fade" id="copyRoleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">复制角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="copyRoleForm">
                        <input type="hidden" id="copySourceRoleId">
                        <div class="mb-3">
                            <label class="form-label">新角色名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="newRoleName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">新角色编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="newRoleCode" required>
                            <div class="form-text">角色编码只能包含大写字母和下划线</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="copyRole()">确认复制</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/wkg/static/js/system/role-management.js"></script>
</body>
</html>
