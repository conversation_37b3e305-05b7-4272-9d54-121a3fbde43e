<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限管理 - 端智AI助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .permission-tree {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }
        .permission-node {
            margin: 8px 0;
            padding: 8px;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .permission-node.level-0 {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .permission-node.level-1 {
            background: #f3e5f5;
            border-color: #9c27b0;
            margin-left: 20px;
        }
        .permission-node.level-2 {
            background: #e8f5e8;
            border-color: #4caf50;
            margin-left: 40px;
        }
        .permission-type-badge {
            font-size: 0.75rem;
        }
        .permission-actions {
            opacity: 0;
            transition: opacity 0.2s;
        }
        .permission-node:hover .permission-actions {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-key-fill me-2"></i>权限管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">系统管理</a></li>
                    <li class="breadcrumb-item active">权限管理</li>
                </ol>
            </nav>
        </div>

        <div class="row">
            <!-- 权限树 -->
            <div class="col-md-8">
                <div class="permission-tree">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5><i class="bi bi-diagram-3 me-2"></i>权限树结构</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                <i class="bi bi-arrows-expand"></i> 展开全部
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="collapseAll()">
                                <i class="bi bi-arrows-collapse"></i> 收起全部
                            </button>
                            <button type="button" class="btn btn-sm btn-success" onclick="showCreateModal()">
                                <i class="bi bi-plus-lg"></i> 新增权限
                            </button>
                        </div>
                    </div>
                    <div id="permissionTreeContainer">
                        <!-- 权限树将通过JS动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 权限详情 -->
            <div class="col-md-4">
                <div class="table-container">
                    <h5><i class="bi bi-info-circle me-2"></i>权限详情</h5>
                    <div id="permissionDetail">
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-cursor-text" style="font-size: 3rem;"></i>
                            <p class="mt-2">请选择一个权限查看详情</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑权限模态框 -->
    <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="permissionModalTitle">新增权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="permissionForm">
                        <input type="hidden" id="permissionId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">权限名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="permissionName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">权限编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="permissionCode" required>
                                    <div class="form-text">权限编码格式：module:resource:action</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">权限类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="permissionType" required>
                                        <option value="">请选择类型</option>
                                        <option value="MENU">菜单权限</option>
                                        <option value="API">接口权限</option>
                                        <option value="BUTTON">按钮权限</option>
                                        <option value="DATA">数据权限</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">父权限</label>
                                    <select class="form-select" id="parentId">
                                        <option value="0">顶级权限</option>
                                        <!-- 父权限选项将通过JS动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">资源URL</label>
                                    <input type="text" class="form-control" id="resourceUrl">
                                    <div class="form-text">API权限必填</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">HTTP方法</label>
                                    <select class="form-select" id="httpMethod">
                                        <option value="">请选择方法</option>
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                        <option value="PATCH">PATCH</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">图标</label>
                                    <input type="text" class="form-control" id="icon" placeholder="bi-house-fill">
                                    <div class="form-text">Bootstrap Icons图标类名</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">排序</label>
                                    <input type="number" class="form-control" id="sortOrder" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="status">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">描述</label>
                                    <textarea class="form-control" id="description" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePermission()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/wkg/static/js/system/permission-management.js"></script>
</body>
</html>
