<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限调试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>权限调试信息</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>用户登录状态</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>是否已登录:</strong> <span th:text="${@stpUtil.isLogin()}">false</span></p>
                        <p><strong>用户ID:</strong> <span th:text="${@stpUtil.getLoginId()}">未登录</span></p>
                        <p><strong>用户名:</strong> <span th:text="${@stpUtil.getUsername()}">未登录</span></p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>权限检查结果</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>menu:system:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:system')}" 
                                 th:class="${@stpUtil.hasPermission('menu:system')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                        <p><strong>menu:view:permission_group:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:view:permission_group')}" 
                                 th:class="${@stpUtil.hasPermission('menu:view:permission_group')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                        <p><strong>menu:view:users:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:view:users')}" 
                                 th:class="${@stpUtil.hasPermission('menu:view:users')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                        <p><strong>menu:view:roles:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:view:roles')}" 
                                 th:class="${@stpUtil.hasPermission('menu:view:roles')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                        <p><strong>menu:view:permissions:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:view:permissions')}" 
                                 th:class="${@stpUtil.hasPermission('menu:view:permissions')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                        <p><strong>menu:view:departments:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:view:departments')}" 
                                 th:class="${@stpUtil.hasPermission('menu:view:departments')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                        <p><strong>menu:view:logs:</strong> 
                           <span th:text="${@stpUtil.hasPermission('menu:view:logs')}" 
                                 th:class="${@stpUtil.hasPermission('menu:view:logs')} ? 'text-success' : 'text-danger'">false</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>权限管理菜单显示测试</h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${@stpUtil.hasPermission('menu:view:permission_group')}" class="alert alert-success">
                            ✅ 权限管理主菜单应该显示 (menu:view:permission_group)
                        </div>
                        <div th:unless="${@stpUtil.hasPermission('menu:view:permission_group')}" class="alert alert-danger">
                            ❌ 权限管理主菜单不会显示 (缺少 menu:view:permission_group 权限)
                        </div>
                        
                        <div th:if="${@stpUtil.hasPermission('menu:system')}" class="alert alert-warning">
                            ⚠️ 检测到旧的权限编码 menu:system (应该已经不使用)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
