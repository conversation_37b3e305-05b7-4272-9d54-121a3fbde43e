spring.application.name=mcp-server

spring.ai.mcp.server.name=mcp-server
spring.ai.mcp.server.version=0.0.1
spring.ai.mcp.server.sse-endpoint=/sse

spring.ai.mcp.server.description=mcp集合

# Server port
server.port=8081

server.servlet.context-path=/aistudio

spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.thymeleaf.cache=false
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0

# ================= 数据库配置 =================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# 数据库连接URL (请替换 your_db_name)
spring.datasource.url=jdbc:mysql://**************:3306/aistudio?allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&useSSL=false
spring.datasource.username=root
spring.datasource.password=wkg@llqzer1123

# ================= MyBatis Plus配置 =================
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.zibbava.edgemind.server.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# ================= Redis配置 =================
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=10000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# ================= Sa-Token配置 =================
# token名称 (同时也是cookie名称)
sa-token.token-name=satoken
# token有效期，单位s 默认30天, -1代表永不过期
sa-token.timeout=2592000
# 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
sa-token.is-concurrent=true
# 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
sa-token.is-share=true
# token风格
sa-token.token-style=uuid
# 是否输出操作日志
sa-token.is-log=false
