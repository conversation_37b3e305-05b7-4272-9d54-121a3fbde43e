<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>许可证管理 - EdgeMind</title>
    <link href="/aistudio/css/vendor/bootstrap.min.css" rel="stylesheet">
    <link href="/aistudio/css/vendor/bootstrap-icons.css" rel="stylesheet">
    <link href="/aistudio/css/features/admin/license-management.css" rel="stylesheet">

</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-shield-check"></i> EdgeMind 许可证管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/aistudio/admin/main">
                    <i class="bi bi-house"></i> 返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card statistics-card text-center">
                    <div class="card-body">
                        <i class="bi bi-collection text-primary" style="font-size: 2rem;"></i>
                        <h4 class="mt-2 mb-0" id="totalCount">-</h4>
                        <small class="text-muted">总许可证数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card statistics-card text-center">
                    <div class="card-body">
                        <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                        <h4 class="mt-2 mb-0" id="activeCount">-</h4>
                        <small class="text-muted">已激活</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card statistics-card text-center">
                    <div class="card-body">
                        <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                        <h4 class="mt-2 mb-0" id="expiringSoonCount">-</h4>
                        <small class="text-muted">即将过期</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card statistics-card text-center">
                    <div class="card-body">
                        <i class="bi bi-gift text-info" style="font-size: 2rem;"></i>
                        <h4 class="mt-2 mb-0" id="freeTrialCount">-</h4>
                        <small class="text-muted">免费试用</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和操作区域 -->
        <div class="search-section">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">许可证类型</label>
                    <select class="form-select" id="licenseTypeFilter">
                        <option value="">全部类型</option>
                        <option value="1">免费试用</option>
                        <option value="2">管理员生成</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="1">未激活</option>
                        <option value="2">已激活</option>
                        <option value="3">已过期</option>
                        <option value="4">已禁用</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">硬件指纹</label>
                    <input type="text" class="form-control" id="hardwareFingerprintFilter" placeholder="输入硬件指纹">
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作</label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary" onclick="searchLicenses()">
                            <i class="bi bi-search"></i> 搜索
                        </button>
                        <button class="btn btn-success" onclick="showGenerateLicenseModal()">
                            <i class="bi bi-plus"></i> 生成许可证
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 许可证列表 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">许可证列表</h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="exportLicenses()">
                        <i class="bi bi-download"></i> 导出
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>许可证密钥</th>
                                <th>硬件指纹</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>过期时间</th>
                                <th>剩余天数</th>
                                <th>生成者</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="licenseTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页信息和分页控件 -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted" id="paginationInfo">
                        <!-- 分页信息将通过JavaScript动态生成 -->
                    </div>
                    <nav aria-label="许可证分页">
                        <ul class="pagination mb-0" id="pagination">
                            <!-- 分页将通过JavaScript动态生成 -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成许可证模态框 -->
    <div class="modal fade" id="generateLicenseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">生成许可证</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="generateLicenseForm">
                        <div class="mb-3">
                            <label class="form-label">硬件指纹 *</label>
                            <input type="text" class="form-control" id="hardwareFingerprint" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">系统标识符 *</label>
                            <input type="text" class="form-control" id="systemIdentifier" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">过期时间 *</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <select class="form-select" id="expireTimePreset" onchange="setExpireTime()">
                                        <option value="">选择快速时间</option>
                                        <option value="1">1天</option>
                                        <option value="7">7天</option>
                                        <option value="30">1个月</option>
                                        <option value="90">3个月</option>
                                        <option value="180">6个月</option>
                                        <option value="365">1年</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <input type="datetime-local" class="form-control" id="expireTime" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">生成者</label>
                            <input type="text" class="form-control" id="generatedBy" placeholder="默认为ADMIN">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="generateLicense()">生成许可证</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 许可证详情模态框 -->
    <div class="modal fade" id="licenseDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">许可证详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="licenseDetailContent">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/aistudio/js/vendor/bootstrap.bundle.min.js"></script>
    <script src="/aistudio/js/common/api-utils.js"></script>
    <script src="/aistudio/js/features/admin/license-management.js"></script>
</body>
</html>