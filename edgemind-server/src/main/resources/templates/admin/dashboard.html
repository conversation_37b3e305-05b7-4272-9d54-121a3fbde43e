<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeMind 管理后台 - 仪表盘</title>
    
    <!-- Bootstrap CSS -->
    <link href="/aistudio/css/vendor/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/aistudio/css/vendor/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="/aistudio/js/vendor/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="/aistudio/css/features/admin/dashboard.css" rel="stylesheet">





</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain me-2"></i>EdgeMind 管理后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span th:if="${currentAdmin}" th:text="${currentAdmin.nickname}">管理员</span>
                        <span th:unless="${currentAdmin}">管理员</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/aistudio/admin/change-password">
                            <i class="fas fa-key me-2"></i>修改密码
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 操作按钮 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>数据概览</h2>
            <button onclick="refreshData()" class="btn btn-primary">
                <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                <i class="fas fa-sync-alt me-2"></i>刷新数据
            </button>
        </div>

        <!-- 统计卡片 -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card total h-100">
                    <div class="card-body text-center">
                        <div class="icon text-primary">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="number text-primary" id="totalDownloads" th:text="${statistics?.totalDownloads ?: 0}">0</div>
                        <div class="text-muted">总下载次数</div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card stat-card today h-100">
                    <div class="card-body text-center">
                        <div class="icon text-success">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="number text-success" id="todayDownloads" th:text="${statistics?.todayDownloads ?: 0}">0</div>
                        <div class="text-muted">今日下载</div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card stat-card month h-100">
                    <div class="card-body text-center">
                        <div class="icon text-warning">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="number text-warning" id="monthDownloads" th:text="${statistics?.thisMonthDownloads ?: 0}">0</div>
                        <div class="text-muted">本月下载</div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6">
                <div class="card stat-card unique h-100">
                    <div class="card-body text-center">
                        <div class="icon text-danger">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="number text-danger" id="uniqueIps" th:text="${statistics?.uniqueIpCount ?: 0}">0</div>
                        <div class="text-muted">独立IP</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-area me-2 text-primary"></i>最近7天下载趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sevenDaysChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar me-2 text-success"></i>最近30天下载趋势
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="thirtyDaysChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aistudio/js/vendor/bootstrap.bundle.min.js"></script>
    <!-- API 工具类 -->
    <script src="/aistudio/js/common/api-utils.js"></script>
    <!-- 仪表板脚本 -->
    <script src="/aistudio/js/features/admin/dashboard.js"></script>
    
    <script>
        // 传递服务器端数据到前端
        window.statisticsData = /*[[${statistics}]]*/ null;
    </script>
</body>
</html>