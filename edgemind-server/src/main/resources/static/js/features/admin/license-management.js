/**
 * 许可证管理页面JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 10; // 每页显示10条记录
let totalPages = 0;

// 页面加载完成后初始化
// 许可证管理页面初始化函数
function initializeLicenseManagement() {
    loadStatistics();
    loadLicenseList();
    
    // 设置默认过期时间为一年后
    const expireTimeInput = document.getElementById('expireTime');
    if (expireTimeInput) {
        const oneYearLater = new Date();
        oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
        expireTimeInput.value = oneYearLater.toISOString().slice(0, 16);
    }
}

// 由于许可证管理页面通过iframe独立加载，保留自动初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeLicenseManagement();
});

/**
 * 加载统计信息
 */
function loadStatistics() {
    apiRequest('/aistudio/api/license-records/statistics', 'GET')
        .then(response => {
            if (response.code === 200) {
                const stats = response.data;
                document.getElementById('totalCount').textContent = stats.totalCount || 0;
                document.getElementById('activeCount').textContent = stats.activeCount || 0;
                document.getElementById('expiringSoonCount').textContent = stats.expiringSoonCount || 0;
                document.getElementById('freeTrialCount').textContent = stats.freeTrialCount || 0;
            }
        })
        .catch(error => {
            console.error('加载统计信息失败:', error);
        });
}

/**
 * 加载许可证列表
 */
function loadLicenseList(page = 1) {
    currentPage = page;
    
    const queryParams = {
        pageNum: currentPage,
        pageSize: pageSize,
        licenseType: document.getElementById('licenseTypeFilter')?.value || null,
        status: document.getElementById('statusFilter')?.value || null,
        hardwareFingerprint: document.getElementById('hardwareFingerprintFilter')?.value || null
    };
    
    // 移除空值
    Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === null || queryParams[key] === '') {
            delete queryParams[key];
        }
    });
    
    apiRequest('/aistudio/api/license-records/page', 'POST', queryParams)
        .then(response => {
            if (response.code === 200) {
                const pageData = response.data;
                console.log('分页数据:', pageData); // 调试信息
                renderLicenseTable(pageData.records);
                renderPagination(pageData.current, pageData.pages, pageData.total);
                totalPages = pageData.pages;
            } else {
                showAlert('加载许可证列表失败: ' + response.message, 'danger');
            }
        })
        .catch(error => {
            console.error('加载许可证列表失败:', error);
            showAlert('加载许可证列表失败', 'danger');
        });
}

/**
 * 渲染许可证表格
 */
function renderLicenseTable(licenses) {
    const tbody = document.getElementById('licenseTableBody');
    if (!tbody) return;
    
    if (!licenses || licenses.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = licenses.map(license => {
        const statusBadge = getStatusBadge(license.status, license.statusDesc);
        const typeBadge = getTypeBadge(license.licenseType, license.licenseTypeDesc);
        const remainingDays = getRemainingDaysDisplay(license.remainingDays, license.expiringSoon);
        
        return `
            <tr ${license.expiringSoon ? 'class="table-warning"' : ''}>
                <td>
                    <code class="text-truncate d-inline-block" style="max-width: 150px;" title="${license.licenseKey}">
                        ${license.licenseKey}
                    </code>
                    <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('${license.licenseKey}')" title="复制">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </td>
                <td>
                    <code class="text-truncate d-inline-block" style="max-width: 120px;" title="${license.hardwareFingerprint}">
                        ${license.hardwareFingerprint}
                    </code>
                </td>
                <td>${typeBadge}</td>
                <td>${statusBadge}</td>
                <td>${formatDateTime(license.createTime)}</td>
                <td>${formatDateTime(license.expireTime)}</td>
                <td>${remainingDays}</td>
                <td>${license.generatedBy || '-'}</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-info" onclick="showLicenseDetail(${license.id})" title="详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${getActionButtons(license)}
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

/**
 * 获取状态徽章
 */
function getStatusBadge(status, statusDesc) {
    const badgeClasses = {
        0: 'bg-secondary',  // 未激活
        1: 'bg-success',    // 已激活
        2: 'bg-danger',     // 已过期
        3: 'bg-warning'     // 已禁用
    };
    
    const badgeClass = badgeClasses[status] || 'bg-secondary';
    return `<span class="badge ${badgeClass} status-badge">${statusDesc}</span>`;
}

/**
 * 获取类型徽章
 */
function getTypeBadge(type, typeDesc) {
    const badgeClasses = {
        1: 'bg-info',       // 免费试用
        2: 'bg-primary'     // 管理员生成
    };
    
    const badgeClass = badgeClasses[type] || 'bg-secondary';
    return `<span class="badge ${badgeClass} status-badge">${typeDesc}</span>`;
}

/**
 * 获取剩余天数显示
 */
function getRemainingDaysDisplay(remainingDays, expiringSoon) {
    if (remainingDays === -1) {
        return '<span class="text-muted">永不过期</span>';
    } else if (remainingDays <= 0) {
        return '<span class="text-danger">已过期</span>';
    } else {
        const className = expiringSoon ? 'text-warning expiring-soon' : 'text-success';
        return `<span class="${className}">${remainingDays} 天</span>`;
    }
}

/**
 * 获取操作按钮
 */
function getActionButtons(license) {
    let buttons = '';
    
    // 仅保留删除按钮 - 状态管理由本地客户端处理
    buttons += `
        <button class="btn btn-outline-danger" onclick="deleteLicense(${license.id})" title="删除">
            <i class="bi bi-trash"></i>
        </button>
    `;
    
    return buttons;
}

/**
 * 渲染分页
 */
function renderPagination(current, total, totalRecords) {
    const pagination = document.getElementById('pagination');
    const paginationInfo = document.getElementById('paginationInfo');
    
    if (!pagination) return;
    
    // 更新分页信息
    if (paginationInfo) {
        if (totalRecords === 0) {
            paginationInfo.innerHTML = '暂无数据';
        } else {
            const startRecord = (current - 1) * pageSize + 1;
            const endRecord = Math.min(current * pageSize, totalRecords);
            paginationInfo.innerHTML = `显示第 ${startRecord}-${endRecord} 条，共 ${totalRecords} 条记录`;
        }
    }
    
    if (total <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHtml = '';
    
    // 上一页
    paginationHtml += `
        <li class="page-item ${current <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadLicenseList(${current - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(total, current + 2);
    
    if (startPage > 1) {
        paginationHtml += '<li class="page-item"><a class="page-link" href="#" onclick="loadLicenseList(1)">1</a></li>';
        if (startPage > 2) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === current ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadLicenseList(${i})">${i}</a>
            </li>
        `;
    }
    
    if (endPage < total) {
        if (endPage < total - 1) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadLicenseList(${total})">${total}</a></li>`;
    }
    
    // 下一页
    paginationHtml += `
        <li class="page-item ${current >= total ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadLicenseList(${current + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = paginationHtml;
}

/**
 * 搜索许可证
 */
function searchLicenses() {
    loadLicenseList(1);
}

/**
 * 刷新数据
 */
function refreshData() {
    loadStatistics();
    loadLicenseList(currentPage);
}

/**
 * 设置过期时间
 */
function setExpireTime() {
    const preset = document.getElementById('expireTimePreset').value;
    const expireTimeInput = document.getElementById('expireTime');
    
    if (preset && preset !== 'custom') {
        const days = parseInt(preset);
        const now = new Date();
        const expireDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
        
        // 格式化为 datetime-local 输入格式
        const year = expireDate.getFullYear();
        const month = String(expireDate.getMonth() + 1).padStart(2, '0');
        const day = String(expireDate.getDate()).padStart(2, '0');
        const hours = String(expireDate.getHours()).padStart(2, '0');
        const minutes = String(expireDate.getMinutes()).padStart(2, '0');
        
        const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
        expireTimeInput.value = formattedDate;
    }
}

/**
 * 显示生成许可证模态框
 */
function showGenerateLicenseModal() {
    console.log('showGenerateLicenseModal called'); // 调试日志
    try {
        const modalElement = document.getElementById('generateLicenseModal');
        if (!modalElement) {
            console.error('Modal element not found: generateLicenseModal');
            return;
        }
        
        // 重置表单
        const form = document.getElementById('generateLicenseForm');
        if (form) {
            form.reset();
        }
        
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } catch (error) {
        console.error('Error showing modal:', error);
    }
}

/**
 * 生成许可证
 */
function generateLicense() {
    console.log('generateLicense called'); // 调试日志
    const form = document.getElementById('generateLicenseForm');
    if (!form) {
        console.error('Form not found: generateLicenseForm');
        return;
    }
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const licenseInfo = {
        hardwareFingerprint: document.getElementById('hardwareFingerprint').value,
        systemIdentifier: document.getElementById('systemIdentifier').value,
        expireTime: document.getElementById('expireTime').value
    };
    
    const generatedBy = document.getElementById('generatedBy').value || 'ADMIN';
    
    apiRequest(`/aistudio/api/license-records/admin/generate?generatedBy=${encodeURIComponent(generatedBy)}`, 'POST', licenseInfo)
        .then(response => {
            if (response.code === 200) {
                showAlert('许可证生成成功！许可证密钥：' + response.data, 'success');
                bootstrap.Modal.getInstance(document.getElementById('generateLicenseModal')).hide();
                form.reset();
                refreshData();
            } else {
                showAlert('许可证生成失败：' + response.message, 'danger');
            }
        })
        .catch(error => {
            console.error('生成许可证失败:', error);
            showAlert('生成许可证失败', 'danger');
        });
}

// 已移除激活许可证功能 - 由本地客户端处理

// 已移除禁用许可证功能 - 由本地客户端处理

// 已移除更新许可证状态功能 - 由本地客户端处理

/**
 * 删除许可证
 */
function deleteLicense(id) {
    if (!confirm('确定要删除这个许可证记录吗？此操作不可恢复！')) {
        return;
    }
    
    apiRequest(`/aistudio/api/license-records/${id}`, 'DELETE')
        .then(response => {
            if (response.code === 200) {
                showAlert('许可证记录删除成功', 'success');
                refreshData();
            } else {
                showAlert('许可证记录删除失败：' + response.message, 'danger');
            }
        })
        .catch(error => {
            console.error('删除许可证记录失败:', error);
            showAlert('删除许可证记录失败', 'danger');
        });
}

/**
 * 显示许可证详情
 */
function showLicenseDetail(id) {
    apiRequest(`/aistudio/api/license-records/${id}`, 'GET')
        .then(response => {
            if (response.code === 200) {
                const license = response.data;
                renderLicenseDetail(license);
                const modal = new bootstrap.Modal(document.getElementById('licenseDetailModal'));
                modal.show();
            } else {
                showAlert('获取许可证详情失败：' + response.message, 'danger');
            }
        })
        .catch(error => {
            console.error('获取许可证详情失败:', error);
            showAlert('获取许可证详情失败', 'danger');
        });
}

/**
 * 渲染许可证详情
 */
function renderLicenseDetail(license) {
    const content = document.getElementById('licenseDetailContent');
    if (!content) return;
    
    const statusBadge = getStatusBadge(license.status, license.statusDesc);
    const typeBadge = getTypeBadge(license.licenseType, license.licenseTypeDesc);
    const remainingDays = getRemainingDaysDisplay(license.remainingDays, license.expiringSoon);
    
    content.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td style="width: 30%; vertical-align: top;"><strong>许可证类型:</strong></td><td>${typeBadge}</td></tr>
                    <tr><td style="width: 30%; vertical-align: top;"><strong>状态:</strong></td><td>${statusBadge}</td></tr>
                    <tr><td style="width: 30%; vertical-align: top;"><strong>生成者:</strong></td><td>${license.generatedBy || '-'}</td></tr>
                    <tr><td style="width: 30%; vertical-align: top;"><strong>备注:</strong></td><td>${license.remark || '-'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>时间信息</h6>
                <table class="table table-sm">
                    <tr><td style="width: 30%;"><strong>创建时间:</strong></td><td>${formatDateTime(license.createTime)}</td></tr>
                    <tr><td style="width: 30%;"><strong>激活时间:</strong></td><td>${formatDateTime(license.activatedTime) || '-'}</td></tr>
                    <tr><td style="width: 30%;"><strong>过期时间:</strong></td><td>${formatDateTime(license.expireTime) || '-'}</td></tr>
                    <tr><td style="width: 30%;"><strong>剩余天数:</strong></td><td>${remainingDays}</td></tr>
                    <tr><td style="width: 30%;"><strong>更新时间:</strong></td><td>${formatDateTime(license.updateTime) || '-'}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>许可证密钥</h6>
                <div class="card">
                    <div class="card-body p-2">
                        <code style="word-break: break-all; white-space: pre-wrap; font-size: 0.85em;">${license.licenseKey}</code>
                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${license.licenseKey}')" title="复制许可证密钥">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-6">
                <h6>硬件指纹</h6>
                <div class="card">
                    <div class="card-body p-2">
                        <code style="word-break: break-all; white-space: pre-wrap; font-size: 0.85em;">${license.hardwareFingerprint}</code>
                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${license.hardwareFingerprint}')" title="复制硬件指纹">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <h6>系统标识符</h6>
                <div class="card">
                    <div class="card-body p-2">
                        <code style="word-break: break-all; white-space: pre-wrap; font-size: 0.85em;">${license.systemIdentifier || '-'}</code>
                        ${license.systemIdentifier ? `<button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('${license.systemIdentifier}')" title="复制系统标识符"><i class="bi bi-clipboard"></i></button>` : ''}
                    </div>
                </div>
            </div>
        </div>
        ${license.clientInfo ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>客户端信息</h6>
                <div class="card">
                    <div class="card-body p-2">
                        <small style="word-break: break-all; white-space: pre-wrap;">${license.clientInfo}</small>
                    </div>
                </div>
            </div>
        </div>
        ` : ''}
    `;
}

// 已移除更新过期状态功能 - 仅保留记录查询功能

/**
 * 导出许可证（简单实现）
 */
function exportLicenses() {
    // 这里可以实现导出功能，比如导出为CSV或Excel
    showAlert('导出功能开发中...', 'info');
}



/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateTimeStr;
    }
}

/**
 * 复制到剪贴板（增强版）
 */
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showAlert('已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

/**
 * 兼容性复制方法
 */
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showAlert('已复制到剪贴板', 'success');
        } else {
            showAlert('复制失败', 'danger');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showAlert('复制失败', 'danger');
    } finally {
        document.body.removeChild(textArea);
    }
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}