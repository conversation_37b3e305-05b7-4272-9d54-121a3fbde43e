/**
 * Dashboard 页面功能模块
 */
class DashboardManager {
    constructor() {
        this.sevenDaysChartInstance = null;
        this.thirtyDaysChartInstance = null;
        this.init();
    }

    /**
     * 初始化页面
     */
    init() {
        this.bindEvents();
        this.initCharts();
        this.loadStatistics();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 刷新按钮事件
        const refreshBtn = document.querySelector('[onclick="refreshData()"]');
        if (refreshBtn) {
            refreshBtn.removeAttribute('onclick');
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // 退出登录事件
        const logoutBtn = document.querySelector('[onclick="logout()"]');
        if (logoutBtn) {
            logoutBtn.removeAttribute('onclick');
            logoutBtn.addEventListener('click', () => this.logout());
        }
    }

    /**
     * 退出登录
     */
    async logout() {
        if (confirm('确定要退出登录吗？')) {
            try {
                const result = await ApiUtils.post('/admin/logout');
                if (result.code === 200) {
                    ApiUtils.redirectToLogin();
                } else {
                    ApiUtils.showAlert('退出失败：' + result.message, 'danger');
                }
            } catch (error) {
                ApiUtils.showAlert('网络错误，请稍后重试', 'danger');
            }
        }
    }

    /**
     * 刷新数据
     */
    async refreshData() {
        const button = document.querySelector('[onclick="refreshData()"]');
        const spinner = button ? button.querySelector('.loading-spinner') : null;
        
        try {
            // 显示加载状态
            if (button && spinner) {
                this.setLoadingState(button, spinner, true);
            }
            
            const result = await ApiUtils.get('/admin/statistics');
            
            if (result.code === 200) {
                const data = result.data;
                this.updateStatistics(data);
                this.updateCharts(data);
                ApiUtils.showAlert('数据刷新成功', 'success');
            } else {
                ApiUtils.showAlert('刷新失败：' + result.message, 'danger');
            }
        } catch (error) {
            ApiUtils.showAlert('网络错误，请稍后重试', 'danger');
        } finally {
            // 隐藏加载状态
            if (button && spinner) {
                this.setLoadingState(button, spinner, false);
            }
        }
    }

    /**
     * 设置加载状态
     */
    setLoadingState(button, spinner, loading) {
        if (loading) {
            spinner.classList.add('show');
            button.disabled = true;
        } else {
            spinner.classList.remove('show');
            button.disabled = false;
        }
    }
    
    /**
     * 更新统计数据
     */
    updateStatistics(data) {
        const elements = {
            totalDownloads: document.getElementById('totalDownloads'),
            todayDownloads: document.getElementById('todayDownloads'),
            monthDownloads: document.getElementById('monthDownloads'),
            uniqueIps: document.getElementById('uniqueIps')
        };

        if (elements.totalDownloads) {
            elements.totalDownloads.textContent = ApiUtils.formatNumber(data.totalDownloads || 0);
        }
        if (elements.todayDownloads) {
            elements.todayDownloads.textContent = ApiUtils.formatNumber(data.todayDownloads || 0);
        }
        if (elements.monthDownloads) {
            elements.monthDownloads.textContent = ApiUtils.formatNumber(data.thisMonthDownloads || 0);
        }
        if (elements.uniqueIps) {
            elements.uniqueIps.textContent = ApiUtils.formatNumber(data.uniqueIpCount || 0);
        }
    }

    /**
     * 创建图表
     */
    createChart(canvasId, data, label, color) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.warn(`Canvas element with id '${canvasId}' not found`);
            return null;
        }

        // 销毁已存在的Chart实例
        if (canvasId === 'sevenDaysChart' && this.sevenDaysChartInstance) {
            this.sevenDaysChartInstance.destroy();
            this.sevenDaysChartInstance = null;
        }
        if (canvasId === 'thirtyDaysChart' && this.thirtyDaysChartInstance) {
            this.thirtyDaysChartInstance.destroy();
            this.thirtyDaysChartInstance = null;
        }

        const ctx = canvas.getContext('2d');
        
        return new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.date),
                datasets: [{
                    label: label,
                    data: data.map(item => item.count),
                    borderColor: color,
                    backgroundColor: color + '20',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            color: '#f0f0f0'
                        }
                    }
                }
            }
        });
    }
    
    /**
     * 更新图表数据
     * @param {Object} data 统计数据
     */
    updateCharts(data) {
        if (!data) return;
        
        // 更新7天图表
         if (data.recentSevenDays) {
             // 确保数据存在且格式正确
             const sevenDaysData = Array.isArray(data.recentSevenDays) ? data.recentSevenDays : [];
             
             // 创建或更新图表
             if (!this.sevenDaysChartInstance) {
                 this.sevenDaysChartInstance = this.createChart('sevenDaysChart', sevenDaysData, '下载次数', '#0d6efd');
             } else {
                 this.sevenDaysChartInstance.data.labels = sevenDaysData.map(item => item.date);
                 this.sevenDaysChartInstance.data.datasets[0].data = sevenDaysData.map(item => item.count);
                 this.sevenDaysChartInstance.update();
             }
         }
         
         // 更新30天图表
         if (data.recentThirtyDays) {
             // 确保数据存在且格式正确
             const thirtyDaysData = Array.isArray(data.recentThirtyDays) ? data.recentThirtyDays : [];
             
             // 创建或更新图表
             if (!this.thirtyDaysChartInstance) {
                 this.thirtyDaysChartInstance = this.createChart('thirtyDaysChart', thirtyDaysData, '下载次数', '#198754');
             } else {
                 this.thirtyDaysChartInstance.data.labels = thirtyDaysData.map(item => item.date);
                 this.thirtyDaysChartInstance.data.datasets[0].data = thirtyDaysData.map(item => item.count);
                 this.thirtyDaysChartInstance.update();
             }
         }
    }
    
    /**
     * 生成模拟数据（用于演示）
     */
    generateMockData(days) {
        const data = [];
        const today = new Date();
        
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            data.push({
                date: date.toISOString().split('T')[0],
                count: Math.floor(Math.random() * 100) + 10
            });
        }
        
        return data;
    }

    /**
     * 初始化图表
     */
    initCharts() {
        // 检查Chart.js是否已加载
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js not loaded, charts will not be displayed');
            return;
        }

        // 检查是否有服务器端传递的统计数据
        const statisticsData = window.statisticsData;
        
        if (statisticsData && (statisticsData.recentSevenDays || statisticsData.recentThirtyDays)) {
            // 使用真实数据
            this.updateStatistics(statisticsData);
            this.updateCharts(statisticsData);
        } else {
            // 从服务器获取真实数据
            this.loadRealTimeData();
        }
    }

    /**
     * 从服务器加载实时数据
     */
    async loadRealTimeData() {
         try {
             const result = await ApiUtils.get('/admin/statistics');
             if (result.code === 200 && result.data) {
                 this.updateStatistics(result.data);
                 this.updateCharts(result.data);
             } else {
                 console.error('API returned error:', result.message || 'Unknown error');
                 this.showFallbackData();
             }
         } catch (error) {
             console.error('Error loading statistics data:', error);
             this.showFallbackData();
         }
     }

    /**
     * 显示备用数据（当无法获取真实数据时）
     */
    showFallbackData() {
        // 显示空数据或错误提示
        this.updateStatistics({
            totalDownloads: 0,
            todayDownloads: 0,
            thisMonthDownloads: 0,
            uniqueIpCount: 0
        });
        
        // 创建空图表
        this.sevenDaysChartInstance = this.createChart('sevenDaysChart', [], '下载次数', '#0d6efd');
        this.thirtyDaysChartInstance = this.createChart('thirtyDaysChart', [], '下载次数', '#198754');
    }

    /**
     * 加载统计数据
     */
    loadStatistics() {
        // 如果有真实统计数据，更新图表
        const statisticsData = window.statisticsData || null;
        if (statisticsData && (statisticsData.recentSevenDays || statisticsData.recentThirtyDays)) {
            this.updateCharts(statisticsData);
        }
    }
};

// 为了向后兼容，保留全局函数
window.logout = function() {
    if (window.dashboardManager) {
        window.dashboardManager.logout();
    }
};

window.refreshData = function() {
    if (window.dashboardManager) {
        window.dashboardManager.refreshData();
    }
};

// 将实例暴露到全局作用域
// 提供初始化函数供按需调用
function initializeDashboard() {
    if (!window.dashboardManager) {
        window.dashboardManager = new DashboardManager();
    }
}

// 兼容原有的自动初始化逻辑（如果页面直接加载此脚本）
if (!window.dashboardManager) {
    window.dashboardManager = null;
    // 如果DOM已经加载完成，直接初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            if (!window.dashboardManager) {
                initializeDashboard();
            }
        });
    } else {
        // DOM已加载完成，直接初始化
        if (!window.dashboardManager) {
            initializeDashboard();
        }
    }
}