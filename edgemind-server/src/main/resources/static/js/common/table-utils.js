/**
 * 表格工具类 - 企业级管理后台表格组件
 * 提供分页、排序、搜索、批量操作等功能
 */
class TableUtils {
    
    constructor(options = {}) {
        this.options = {
            tableSelector: '#dataTable',
            paginationSelector: '#pagination',
            searchSelector: '#searchInput',
            pageSize: 10,
            currentPage: 1,
            totalPages: 0,
            totalRecords: 0,
            apiUrl: '',
            columns: [],
            actions: [],
            searchable: true,
            sortable: true,
            selectable: false,
            ...options
        };
        
        this.data = [];
        this.selectedRows = new Set();
        this.sortField = null;
        this.sortOrder = 'asc';
        this.searchKeyword = '';
        
        this.init();
    }
    
    /**
     * 初始化表格
     */
    init() {
        this.bindEvents();
        this.loadData();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 搜索事件
        if (this.options.searchable) {
            const searchInput = document.querySelector(this.options.searchSelector);
            if (searchInput) {
                searchInput.addEventListener('input', ApiUtils.debounce((e) => {
                    this.searchKeyword = e.target.value;
                    this.currentPage = 1;
                    this.loadData();
                }, 500));
            }
        }
        
        // 表格点击事件委托
        const table = document.querySelector(this.options.tableSelector);
        if (table) {
            table.addEventListener('click', (e) => {
                this.handleTableClick(e);
            });
        }
    }
    
    /**
     * 处理表格点击事件
     */
    handleTableClick(e) {
        const target = e.target;
        
        // 排序
        if (target.classList.contains('sortable')) {
            const field = target.dataset.field;
            this.sort(field);
        }
        
        // 选择行
        if (target.type === 'checkbox' && target.classList.contains('row-select')) {
            const rowId = target.dataset.id;
            this.toggleRowSelection(rowId, target.checked);
        }
        
        // 全选
        if (target.type === 'checkbox' && target.classList.contains('select-all')) {
            this.toggleAllSelection(target.checked);
        }
        
        // 操作按钮
        if (target.classList.contains('action-btn')) {
            const action = target.dataset.action;
            const rowId = target.dataset.id;
            this.handleAction(action, rowId);
        }
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            const params = {
                page: this.options.currentPage,
                size: this.options.pageSize,
                keyword: this.searchKeyword
            };
            
            if (this.sortField) {
                params.sortField = this.sortField;
                params.sortOrder = this.sortOrder;
            }
            
            const response = await ApiUtils.get(this.options.apiUrl, params);
            
            this.data = response.data.records || response.data || [];
            this.options.totalRecords = response.data.total || this.data.length;
            this.options.totalPages = Math.ceil(this.options.totalRecords / this.options.pageSize);
            
            this.renderTable();
            this.renderPagination();
            
        } catch (error) {
            console.error('加载数据失败:', error);
            this.renderEmptyTable();
        }
    }
    
    /**
     * 渲染表格
     */
    renderTable() {
        const table = document.querySelector(this.options.tableSelector);
        if (!table) return;
        
        let html = '<thead><tr>';
        
        // 选择列
        if (this.options.selectable) {
            html += '<th><input type="checkbox" class="select-all"></th>';
        }
        
        // 数据列
        this.options.columns.forEach(column => {
            const sortClass = this.options.sortable && column.sortable ? 'sortable' : '';
            const sortIcon = this.getSortIcon(column.field);
            html += `<th class="${sortClass}" data-field="${column.field}">
                ${column.title} ${sortIcon}
            </th>`;
        });
        
        // 操作列
        if (this.options.actions.length > 0) {
            html += '<th>操作</th>';
        }
        
        html += '</tr></thead><tbody>';
        
        // 数据行
        this.data.forEach(row => {
            html += '<tr>';
            
            // 选择列
            if (this.options.selectable) {
                const checked = this.selectedRows.has(row.id) ? 'checked' : '';
                html += `<td><input type="checkbox" class="row-select" data-id="${row.id}" ${checked}></td>`;
            }
            
            // 数据列
            this.options.columns.forEach(column => {
                let value = this.getNestedValue(row, column.field);
                if (column.formatter) {
                    value = column.formatter(value, row);
                }
                html += `<td>${value || ''}</td>`;
            });
            
            // 操作列
            if (this.options.actions.length > 0) {
                html += '<td>';
                this.options.actions.forEach(action => {
                    if (!action.condition || action.condition(row)) {
                        html += `<button class="btn btn-sm btn-${action.type || 'primary'} action-btn me-1" 
                                        data-action="${action.name}" data-id="${row.id}">
                                    ${action.title}
                                </button>`;
                    }
                });
                html += '</td>';
            }
            
            html += '</tr>';
        });
        
        html += '</tbody>';
        table.innerHTML = html;
    }
    
    /**
     * 渲染空表格
     */
    renderEmptyTable() {
        const table = document.querySelector(this.options.tableSelector);
        if (!table) return;
        
        table.innerHTML = `
            <thead><tr>
                ${this.options.columns.map(col => `<th>${col.title}</th>`).join('')}
            </tr></thead>
            <tbody>
                <tr>
                    <td colspan="${this.options.columns.length}" class="text-center py-4">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <p>暂无数据</p>
                        </div>
                    </td>
                </tr>
            </tbody>
        `;
    }
    
    /**
     * 渲染分页
     */
    renderPagination() {
        const pagination = document.querySelector(this.options.paginationSelector);
        if (!pagination || this.options.totalPages <= 1) {
            if (pagination) pagination.innerHTML = '';
            return;
        }
        
        let html = '<nav><ul class="pagination justify-content-center">';
        
        // 上一页
        const prevDisabled = this.options.currentPage <= 1 ? 'disabled' : '';
        html += `<li class="page-item ${prevDisabled}">
                    <a class="page-link" href="#" data-page="${this.options.currentPage - 1}">上一页</a>
                </li>`;
        
        // 页码
        const startPage = Math.max(1, this.options.currentPage - 2);
        const endPage = Math.min(this.options.totalPages, this.options.currentPage + 2);
        
        if (startPage > 1) {
            html += '<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>';
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const active = i === this.options.currentPage ? 'active' : '';
            html += `<li class="page-item ${active}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>`;
        }
        
        if (endPage < this.options.totalPages) {
            if (endPage < this.options.totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `<li class="page-item">
                        <a class="page-link" href="#" data-page="${this.options.totalPages}">${this.options.totalPages}</a>
                    </li>`;
        }
        
        // 下一页
        const nextDisabled = this.options.currentPage >= this.options.totalPages ? 'disabled' : '';
        html += `<li class="page-item ${nextDisabled}">
                    <a class="page-link" href="#" data-page="${this.options.currentPage + 1}">下一页</a>
                </li>`;
        
        html += '</ul></nav>';
        
        // 分页信息
        const start = (this.options.currentPage - 1) * this.options.pageSize + 1;
        const end = Math.min(this.options.currentPage * this.options.pageSize, this.options.totalRecords);
        html += `<div class="text-center mt-2 text-muted">
                    显示 ${start} - ${end} 条，共 ${this.options.totalRecords} 条记录
                </div>`;
        
        pagination.innerHTML = html;
        
        // 绑定分页点击事件
        pagination.addEventListener('click', (e) => {
            e.preventDefault();
            if (e.target.classList.contains('page-link') && e.target.dataset.page) {
                const page = parseInt(e.target.dataset.page);
                this.goToPage(page);
            }
        });
    }
    
    /**
     * 跳转到指定页
     */
    goToPage(page) {
        if (page < 1 || page > this.options.totalPages) return;
        this.options.currentPage = page;
        this.loadData();
    }
    
    /**
     * 排序
     */
    sort(field) {
        if (this.sortField === field) {
            this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortOrder = 'asc';
        }
        this.loadData();
    }
    
    /**
     * 获取排序图标
     */
    getSortIcon(field) {
        if (!this.options.sortable || this.sortField !== field) {
            return '<i class="fas fa-sort text-muted"></i>';
        }
        return this.sortOrder === 'asc' 
            ? '<i class="fas fa-sort-up text-primary"></i>' 
            : '<i class="fas fa-sort-down text-primary"></i>';
    }
    
    /**
     * 切换行选择
     */
    toggleRowSelection(rowId, checked) {
        if (checked) {
            this.selectedRows.add(rowId);
        } else {
            this.selectedRows.delete(rowId);
        }
        this.updateSelectAllState();
    }
    
    /**
     * 切换全选
     */
    toggleAllSelection(checked) {
        if (checked) {
            this.data.forEach(row => this.selectedRows.add(row.id));
        } else {
            this.selectedRows.clear();
        }
        
        // 更新所有行的选择状态
        const checkboxes = document.querySelectorAll('.row-select');
        checkboxes.forEach(cb => cb.checked = checked);
    }
    
    /**
     * 更新全选状态
     */
    updateSelectAllState() {
        const selectAllCheckbox = document.querySelector('.select-all');
        if (!selectAllCheckbox) return;
        
        const totalRows = this.data.length;
        const selectedCount = this.data.filter(row => this.selectedRows.has(row.id)).length;
        
        selectAllCheckbox.checked = selectedCount === totalRows && totalRows > 0;
        selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalRows;
    }
    
    /**
     * 处理操作
     */
    handleAction(action, rowId) {
        const actionConfig = this.options.actions.find(a => a.name === action);
        if (actionConfig && actionConfig.handler) {
            const row = this.data.find(r => r.id == rowId);
            actionConfig.handler(row, this);
        }
    }
    
    /**
     * 获取嵌套属性值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }
    
    /**
     * 刷新数据
     */
    refresh() {
        this.loadData();
    }
    
    /**
     * 获取选中的行
     */
    getSelectedRows() {
        return this.data.filter(row => this.selectedRows.has(row.id));
    }
    
    /**
     * 清空选择
     */
    clearSelection() {
        this.selectedRows.clear();
        const checkboxes = document.querySelectorAll('.row-select, .select-all');
        checkboxes.forEach(cb => {
            cb.checked = false;
            cb.indeterminate = false;
        });
    }
}

// 全局暴露
window.TableUtils = TableUtils;