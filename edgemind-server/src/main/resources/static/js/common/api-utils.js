/**
 * API工具类 - 企业级管理后台通用组件
 * 提供统一的API调用、错误处理、加载状态管理等功能
 */
class ApiUtils {
    
    /**
     * 默认配置
     */
    static config = {
        baseURL: '/aistudio',
        timeout: 30000,
        showLoading: true,
        showError: true,
        loadingSelector: '.loading-overlay',
        errorContainerSelector: '.error-container'
    };
    
    /**
     * 设置全局配置
     */
    static setConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
    
    /**
     * 显示加载状态
     */
    static showLoading(message = '加载中...') {
        if (!this.config.showLoading) return;
        
        // 移除已存在的加载层
        this.hideLoading();
        
        const loadingHtml = `
            <div class="loading-overlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            ">
                <div style="
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                ">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">${message}</div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', loadingHtml);
    }
    
    /**
     * 隐藏加载状态
     */
    static hideLoading() {
        const loadingElement = document.querySelector(this.config.loadingSelector);
        if (loadingElement) {
            loadingElement.remove();
        }
    }
    
    /**
     * 显示错误信息
     */
    static showError(message, type = 'danger') {
        if (!this.config.showError) return;
        
        // 移除已存在的错误提示
        this.hideError();
        
        const errorHtml = `
            <div class="error-container alert alert-${type} alert-dismissible fade show" 
                 style="position: fixed; top: 20px; right: 20px; z-index: 10000; min-width: 300px;" 
                 role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', errorHtml);
        
        // 自动隐藏
        setTimeout(() => this.hideError(), 5000);
    }
    
    /**
     * 显示成功信息
     */
    static showSuccess(message) {
        this.showError(message, 'success');
    }
    
    /**
     * 隐藏错误信息
     */
    static hideError() {
        const errorElement = document.querySelector(this.config.errorContainerSelector);
        if (errorElement) {
            errorElement.remove();
        }
    }
    
    /**
     * 统一的HTTP请求方法
     */
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include' // 包含cookies
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        // 添加SaToken
        const token = this.getToken();
        if (token) {
            finalOptions.headers['satoken'] = token;
        }
        
        try {
            if (finalOptions.showLoading !== false) {
                this.showLoading(finalOptions.loadingMessage);
            }
            
            // 如果URL已经包含baseURL，则不重复添加
            const finalUrl = url.startsWith(this.config.baseURL) ? url : this.config.baseURL + url;
            const response = await fetch(finalUrl, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            // 处理业务错误
            if (data.code !== 200) {
                if (data.code === 401) {
                    // 未登录，跳转到登录页
                    this.redirectToLogin();
                    return;
                }
                throw new Error(data.message || '请求失败');
            }
            
            return data;
            
        } catch (error) {
            console.error('API请求失败:', error);
            if (finalOptions.showError !== false) {
                this.showError(error.message || '网络请求失败');
            }
            throw error;
        } finally {
            if (finalOptions.showLoading !== false) {
                this.hideLoading();
            }
        }
    }
    
    /**
     * GET请求
     */
    static get(url, params = {}, options = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl, { ...options, method: 'GET' });
    }
    
    /**
     * POST请求
     */
    static post(url, data = {}, options = {}) {
        return this.request(url, {
            ...options,
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * PUT请求
     */
    static put(url, data = {}, options = {}) {
        return this.request(url, {
            ...options,
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    /**
     * DELETE请求
     */
    static delete(url, options = {}) {
        return this.request(url, { ...options, method: 'DELETE' });
    }
    
    /**
     * 获取Token
     */
    static getToken() {
        // 从cookie中获取satoken
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'satoken') {
                return value;
            }
        }
        return null;
    }
    
    /**
     * 跳转到登录页
     */
    static redirectToLogin() {
        const currentPath = window.location.pathname;
        const loginPath = this.config.baseURL + '/admin/login';
        if (currentPath !== loginPath) {
            window.location.href = loginPath;
        }
    }
    
    /**
     * 确认对话框
     */
    static confirm(message, title = '确认操作') {
        return new Promise((resolve) => {
            if (window.confirm(`${title}\n\n${message}`)) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }
    
    /**
     * 表单验证
     */
    static validateForm(formElement) {
        const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }
    
    /**
     * 格式化日期
     */
    static formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }
    
    /**
     * 防抖函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 节流函数
     */
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * 格式化数字显示
     * @param {number} num 要格式化的数字
     * @returns {string} 格式化后的字符串
     */
    static formatNumber(num) {
        if (num === null || num === undefined) return '0';
        const number = parseInt(num);
        if (isNaN(number)) return '0';
        return number.toLocaleString();
    }
    
    /**
     * 显示提示信息
     * @param {string} message 提示消息
     * @param {string} type 提示类型 (success, danger, warning, info)
     */
    static showAlert(message, type = 'info') {
        // 尝试查找页面中的alert容器
        let alertContainer = document.getElementById('alertContainer');
        
        // 如果没有找到容器，创建一个临时容器
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'tempAlertContainer';
            alertContainer.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            document.body.appendChild(alertContainer);
        }
        
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.innerHTML = alertHtml;
        
        // 自动隐藏成功和信息提示
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert && typeof bootstrap !== 'undefined') {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                } else if (alert) {
                    alert.remove();
                }
            }, 3000);
        }
    }
}

// 全局暴露
window.ApiUtils = ApiUtils;

// 向后兼容的全局apiRequest函数
window.apiRequest = function(url, method = 'GET', data = null, options = {}) {
    const requestOptions = {
        method: method.toUpperCase(),
        ...options
    };
    
    if (data && (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT')) {
        requestOptions.body = JSON.stringify(data);
    }
    
    return ApiUtils.request(url, requestOptions);
};