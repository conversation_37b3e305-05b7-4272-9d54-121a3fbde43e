/* 全局样式 */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-gray: #f8fafc;
    --medium-gray: #e2e8f0;
    --dark-gray: #475569;
    --white: #ffffff;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--white);
}

/* 导航栏样式 */
.navbar {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95) !important;
    border-bottom: 1px solid var(--medium-gray);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-link {
    font-weight: 500;
    color: var(--dark-gray) !important;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

/* 导航栏按钮对齐 */
.navbar-nav .nav-item .btn {
    display: flex !important;
    align-items: center !important;
    height: auto !important;
    line-height: 1.5 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0.375rem 0.75rem !important;
    vertical-align: middle !important;
}

.navbar-nav .nav-item {
    display: flex !important;
    align-items: center !important;
}

.navbar-nav .nav-item .nav-link {
    padding: 0.5rem 1rem !important;
    display: flex !important;
    align-items: center !important;
}

/* 确保导航栏整体对齐 */
.navbar-nav {
    align-items: center !important;
}

/* 首页横幅区域 */
.hero-section {
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
    padding-top: 120px;
    min-height: 100vh;
    max-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

/* 响应式调整 - 针对1080p及以下屏幕 */
@media (max-height: 1080px) {
    .hero-section {
        padding-top: 100px;
        padding-bottom: 20px;
    }
    
    .hero-section .display-4 {
        font-size: 2.8rem;
        margin-bottom: 0.8rem;
    }
    
    .hero-section .h4 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }
    
    .hero-section .lead {
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    
    .hero-section p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .chat-window {
        height: 400px;
    }
    
    .feature-icon i {
        font-size: 2.2rem !important;
    }
    
    .hero-section h3 {
        font-size: 1.4rem;
        margin-bottom: 1rem;
    }
    
    .hero-section h5 {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
    }
}

.hero-section .display-4 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.hero-section .btn {
    border-radius: 8px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.hero-section .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 聊天演示容器 */
.chat-demo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.chat-window {
    width: 400px;
    height: 500px;
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    border: 1px solid var(--medium-gray);
}

.chat-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.chat-controls {
    display: flex;
    gap: 8px;
}

.chat-controls i {
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.chat-controls i:hover {
    opacity: 1;
}

.chat-messages {
    height: 380px;
    padding: 16px;
    overflow-y: auto;
    background: var(--light-gray);
}

.message {
    display: flex;
    margin-bottom: 16px;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    margin-right: 12px;
    flex-shrink: 0;
}

.message-content {
    background: var(--white);
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 280px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    line-height: 1.4;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: var(--secondary-color);
    margin-right: 0;
    margin-left: 12px;
}

.user-message .message-content {
    background: var(--primary-color);
    color: var(--white);
}

.chat-input {
    padding: 12px 16px;
    background: var(--white);
    border-top: 1px solid var(--medium-gray);
    display: flex;
    gap: 8px;
}

.chat-input .form-control {
    border: 1px solid var(--medium-gray);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
}

.chat-input .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* 产品优势卡片 */
.advantage-card {
    background: var(--white);
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--medium-gray);
    transition: all 0.3s ease;
    text-align: center;
}

.advantage-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.advantage-icon {
    width: 80px;
    height: 80px;
    background: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: var(--primary-color);
    font-size: 2rem;
}

.advantage-card h4 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

.advantage-card ul {
    text-align: left;
    margin-bottom: 16px;
}

/* 功能特性样式 */
.feature-list {
    padding-left: 0;
}

.feature-item {
    padding: 16px;
    background: var(--light-gray);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.feature-item h5 {
    color: var(--dark-gray);
    margin-bottom: 8px;
}

/* 模型选择器样式 */
.model-demo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.model-selector {
    width: 100%;
    max-width: 400px;
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    padding: 24px;
    border: 1px solid var(--medium-gray);
}

.model-header h6 {
    color: var(--primary-color);
    font-weight: 600;
}

.model-item {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    border: 1px solid var(--medium-gray);
    transition: all 0.3s ease;
    cursor: pointer;
}

.model-item:hover {
    background: var(--light-gray);
    border-color: var(--primary-color);
}

.model-item.active {
    background: var(--light-gray);
    border-color: var(--primary-color);
}

.model-icon {
    width: 40px;
    height: 40px;
    background: var(--light-gray);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 12px;
}

.model-info {
    flex: 1;
}

.model-name {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 14px;
}

.model-desc {
    font-size: 12px;
    color: var(--secondary-color);
}

.thinking-mode {
    padding-top: 16px;
    border-top: 1px solid var(--medium-gray);
}

.form-check-label {
    font-size: 14px;
    color: var(--dark-gray);
}

/* 文档树样式 - 企业级设计 */
.doc-tree-demo {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    padding: 20px;
    border: 1px solid var(--medium-gray);
    position: relative;
}

.doc-tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--medium-gray);
}

.doc-tree-header h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

.tree-item {
    margin-bottom: 4px;
}

.tree-node {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    background: transparent;
    border: 1px solid transparent;
    font-size: 14px;
}

.tree-node:hover {
    background: var(--light-gray);
    border-color: var(--medium-gray);
}

.tree-node i {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.tree-node .bi-folder-fill {
    color: var(--warning-color);
}

.tree-node .bi-file-earmark-pdf {
    color: #dc3545;
}

.tree-node .bi-file-earmark-word {
    color: var(--primary-color);
}

.tree-node .bi-file-earmark-code {
    color: var(--success-color);
}

.tree-children {
    margin-left: 20px;
    border-left: 1px solid var(--medium-gray);
    padding-left: 12px;
    margin-top: 4px;
    transition: all 0.3s ease;
}

.tree-item.collapsed .tree-children {
    display: none;
}

.tree-item.expanded .tree-children {
    display: block;
}

.tree-toggle {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: all 0.2s ease;
    font-size: 10px;
    color: var(--secondary-color);
}

.tree-toggle:hover {
    background: var(--medium-gray);
    color: var(--primary-color);
}

.tree-toggle.expanded::before {
    content: '▼';
}

.tree-toggle.collapsed::before {
    content: '▶';
}

.tree-node.has-children {
    position: relative;
}

.tree-node.has-children .tree-toggle {
    opacity: 1;
}

.tree-node:not(.has-children) .tree-toggle {
    opacity: 0;
    pointer-events: none;
}

.chat-btn {
    opacity: 0;
    transition: all 0.2s ease;
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    margin-left: auto;
}

.tree-node:hover .chat-btn {
    opacity: 1;
}

.chat-btn:hover {
    background: var(--dark-gray);
    transform: scale(1.05);
}

.chat-btn i {
    font-size: 12px;
    margin: 0;
}

/* 技术卡片样式 */
.tech-card {
    background: var(--white);
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--medium-gray);
    transition: all 0.3s ease;
    text-align: center;
}

.tech-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.tech-icon {
    width: 80px;
    height: 80px;
    background: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: var(--primary-color);
    font-size: 2rem;
}

.performance-badge {
    margin-top: 16px;
}

/* 应用场景卡片 */
.scenario-card {
    background: var(--white);
    padding: 24px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--medium-gray);
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
}

.scenario-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.scenario-icon {
    width: 60px;
    height: 60px;
    background: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.efficiency-badge {
    margin-top: 16px;
}

/* 价值卡片样式 */
.value-card {
    background: var(--white);
    padding: 32px 24px;
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--medium-gray);
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
}

.value-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.value-icon {
    width: 80px;
    height: 80px;
    background: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    color: var(--primary-color);
    font-size: 2rem;
}

.value-metrics {
    text-align: center;
}

.metric-item {
    margin-bottom: 20px;
}

.metric-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
}

.metric-desc {
    font-size: 14px;
    color: var(--secondary-color);
    margin-top: 4px;
}

.value-list {
    text-align: left;
}

.value-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    color: var(--dark-gray);
}

/* 联系我们样式 */
.contact-card {
    background: var(--white);
    padding: 40px;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--medium-gray);
}

.contact-item {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: var(--dark-gray);
}

.qr-code {
    text-align: center;
}

.qr-placeholder {
    width: 120px;
    height: 120px;
    background: var(--light-gray);
    border: 2px dashed var(--medium-gray);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: var(--secondary-color);
}

/* 文档树和聊天集成容器样式 - 企业级设计 */
.doc-tree-chat-container {
    background: var(--white);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    display: flex;
    height: 500px;
    border: 1px solid var(--medium-gray);
}

.doc-tree-section {
    width: 45%;
    border-right: 1px solid var(--medium-gray);
    display: flex;
    flex-direction: column;
    background: var(--white);
}

.doc-chat-section {
    width: 55%;
    display: flex;
    flex-direction: column;
    background: var(--white);
}

.doc-tree-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--medium-gray);
    background: var(--light-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    box-sizing: border-box;
}

.doc-tree-header h6 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #000 !important;
}

.doc-tree-header .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.doc-tree {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--medium-gray) transparent;
    background: var(--white);
}

.doc-tree::-webkit-scrollbar {
    width: 6px;
}

.doc-tree::-webkit-scrollbar-track {
    background: transparent;
}

.doc-tree::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 3px;
}

.doc-tree::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

.chat-panel-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--medium-gray);
    background: var(--light-gray);
    height: 60px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

.chat-panel-header h6 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #000 !important;
}

.chat-panel-header i {
    color: #000 !important;
}

.chat-panel-header .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.chat-panel-body {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: var(--white);
}

.chat-messages {
    background: var(--white);
}

/* 文档树聊天栏专用样式 - 避免双滚动条 */
.doc-chat-section .chat-panel-body .chat-messages {
    overflow: visible;
    min-height: 100%;
}

.chat-panel-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--medium-gray);
    background: var(--white);
}

/* 聊天抽屉样式 */
.doc-tree-demo {
    position: relative;
    overflow: visible;
    min-height: 400px;
}



.chat-messages {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.message {
    display: flex;
    gap: 0.75rem;
    max-width: 100%;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.ai-message .message-avatar {
    background: var(--primary-color);
    color: white;
}

.user-message .message-avatar {
    background: #6c757d;
    color: white;
}

.message-content {
    flex: 1;
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.5rem;
    position: relative;
}

.user-message .message-content {
    background: var(--primary-color);
    color: white;
}

.message-content p {
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
}

.message-content small {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-section .display-4 {
        font-size: 2.5rem;
    }

    .chat-window {
        width: 100%;
        max-width: 350px;
    }

    .advantage-card,
    .tech-card,
    .scenario-card,
    .value-card {
        margin-bottom: 24px;
    }

    .model-selector,
    .doc-tree-demo {
        margin-top: 32px;
    }

    .contact-card {
        padding: 24px;
    }

    .qr-placeholder {
        width: 100px;
        height: 100px;
    }


}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 按钮悬停效果 */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 徽章样式 */
.badge {
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.6s ease;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--medium-gray);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}