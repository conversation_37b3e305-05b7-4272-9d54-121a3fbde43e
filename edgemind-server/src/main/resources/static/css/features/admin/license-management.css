/* 许可证管理页面样式 */
.license-card {
    transition: transform 0.2s;
}

.license-card:hover {
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.75rem;
}

.expiring-soon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.search-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.statistics-card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 优化iframe内容显示 */
body {
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding-bottom: 20px;
}

/* 表格容器优化 */
.table-responsive {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}