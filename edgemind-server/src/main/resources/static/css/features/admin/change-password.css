/* 修改密码页面样式 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.password-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    width: 100%;
    max-width: 500px;
    margin: 2rem;
}

.password-header {
    text-align: center;
    margin-bottom: 2rem;
}

.password-header h2 {
    color: #333;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.password-header p {
    color: #666;
    margin: 0;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    height: auto;
    transition: all 0.3s ease;
}

.form-floating .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-floating label {
    color: #666;
    padding: 1rem 0.75rem;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    z-index: 10;
}

.password-toggle:hover {
    color: #333;
}

.btn-change-password {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    width: 100%;
    margin-bottom: 1rem;
}

.btn-change-password:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-back {
    background: #6c757d;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-back:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.password-strength {
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    background: #e9ecef;
    margin-top: 0.25rem;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    width: 0%;
}

.strength-weak .strength-fill {
    background: #dc3545;
    width: 25%;
}

.strength-fair .strength-fill {
    background: #fd7e14;
    width: 50%;
}

.strength-good .strength-fill {
    background: #ffc107;
    width: 75%;
}

.strength-strong .strength-fill {
    background: #28a745;
    width: 100%;
}

.alert {
    border-radius: 12px;
    border: none;
    margin-bottom: 1.5rem;
}

.loading-spinner {
    display: none;
}

.loading-spinner.show {
    display: inline-block;
}