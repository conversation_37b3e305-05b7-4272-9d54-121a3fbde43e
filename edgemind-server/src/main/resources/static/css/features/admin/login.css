/* Login 页面样式 */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #333;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.login-header p {
    color: #666;
    margin: 0;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating > .form-control {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem 0.75rem;
    height: auto;
    transition: all 0.3s ease;
}

.form-floating > .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-floating > label {
    color: #666;
    padding: 1rem 0.75rem;
}

.btn-login {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 0.875rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    width: 100%;
    transition: all 0.3s ease;
    color: white;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-login:disabled {
    opacity: 0.7;
    transform: none;
    box-shadow: none;
}

.remember-forgot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.form-check {
    display: flex;
    align-items: center;
}

.form-check-input {
    margin-right: 0.5rem;
    border-radius: 4px;
}

.form-check-label {
    color: #666;
    cursor: pointer;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #764ba2;
    text-decoration: underline;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 验证码样式 */
.captcha-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.captcha-input {
    flex: 1;
}

.captcha-container .captcha-input {
    margin-bottom: 0;
}

.captcha-image {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.captcha-image img {
    height: 58px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.captcha-image img:hover {
    border-color: #0d6efd;
    transform: scale(1.02);
}

.captcha-refresh {
    padding: 0.5rem;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.captcha-refresh:hover {
    color: #0d6efd;
    background: rgba(13, 110, 253, 0.1);
    transform: rotate(180deg);
}

.captcha-refresh:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0;
    z-index: 10;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #333;
}

.password-input-container {
    position: relative;
}

.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #0f5132;
    border-left: 4px solid #198754;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #842029;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #055160;
    border-left: 4px solid #0dcaf0;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .login-container {
        padding: 2rem 1.5rem;
        margin: 1rem;
        border-radius: 16px;
    }
    
    .login-header h1 {
        font-size: 1.75rem;
    }
    
    .remember-forgot {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .captcha-container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .captcha-image {
        justify-content: center;
    }
    
    .captcha-image img {
        width: 120px;
    }
}

@media (max-width: 400px) {
    .login-container {
        padding: 1.5rem 1rem;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .btn-login {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}