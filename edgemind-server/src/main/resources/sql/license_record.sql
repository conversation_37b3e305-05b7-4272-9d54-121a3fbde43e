-- 统一许可证记录表
CREATE TABLE IF NOT EXISTS `license_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `license_key` text NOT NULL COMMENT '许可证密钥',
    `hardware_fingerprint` varchar(255) NOT NULL COMMENT '硬件指纹',
    `system_identifier` varchar(255) NOT NULL COMMENT '系统标识符',
    `license_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '许可证类型：1-免费试用，2-管理员生成',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-未激活，1-已激活，2-已过期，3-已禁用',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `activated_time` datetime DEFAULT NULL COMMENT '激活时间',
    `expire_time` datetime DEFAULT NULL COMMENT '过期时间，NULL表示永不过期',
    `generated_by` varchar(100) DEFAULT NULL COMMENT '生成者（管理员用户名或系统标识）',
    `client_info` json DEFAULT NULL COMMENT '客户端信息（IP、用户代理等）',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_license_key` (`license_key`(255)) COMMENT '许可证密钥唯一索引',
    KEY `idx_hardware_fingerprint` (`hardware_fingerprint`) COMMENT '硬件指纹索引',
    KEY `idx_license_type` (`license_type`) COMMENT '许可证类型索引',
    KEY `idx_status` (`status`) COMMENT '状态索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引',
    KEY `idx_expire_time` (`expire_time`) COMMENT '过期时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='许可证记录表';

