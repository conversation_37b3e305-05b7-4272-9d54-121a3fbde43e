-- 下载统计表
CREATE TABLE IF NOT EXISTS `download_statistics` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `download_date` date NOT NULL COMMENT '下载日期',
    `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数',
    `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` text DEFAULT NULL COMMENT '用户代理',
    `referer` varchar(500) DEFAULT NULL COMMENT '来源页面',
    `download_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
    `file_name` varchar(255) DEFAULT 'duanzhi.zip' COMMENT '下载文件名',
    `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
    `download_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '下载状态：0-失败，1-成功',
    PRIMARY KEY (`id`),
    KEY `idx_download_date` (`download_date`) COMMENT '下载日期索引',
    KEY `idx_download_time` (`download_time`) COMMENT '下载时间索引',
    KEY `idx_ip_address` (`ip_address`) COMMENT 'IP地址索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='下载统计表';

-- 创建下载统计汇总视图
CREATE OR REPLACE VIEW `v_download_statistics` AS
SELECT 
    DATE(download_time) as stat_date,
    COUNT(*) as total_downloads,
    COUNT(DISTINCT ip_address) as unique_ips,
    SUM(CASE WHEN download_status = 1 THEN 1 ELSE 0 END) as success_downloads,
    SUM(CASE WHEN download_status = 0 THEN 1 ELSE 0 END) as failed_downloads
FROM download_statistics 
GROUP BY DATE(download_time)
ORDER BY stat_date DESC;