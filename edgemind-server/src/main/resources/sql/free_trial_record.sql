CREATE TABLE `free_trial_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hardware_fingerprint` varchar(255) NOT NULL COMMENT '硬件指纹',
  `system_identifier` varchar(255) NOT NULL COMMENT '系统标识符',
  `license_key` varchar(500) NOT NULL COMMENT '许可证密钥',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_hardware_fingerprint` (`hardware_fingerprint`) COMMENT '硬件指纹唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='免费试用记录表';