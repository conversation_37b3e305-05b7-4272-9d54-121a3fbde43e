package com.zibbava.edgemind.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.server.entity.DownloadStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 下载统计Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface DownloadStatisticsMapper extends BaseMapper<DownloadStatistics> {

    /**
     * 获取总下载次数
     *
     * @return 总下载次数
     */
    @Select("SELECT COUNT(*) FROM download_statistics WHERE download_status = 1")
    Long getTotalDownloads();

    /**
     * 获取今日下载次数
     *
     * @return 今日下载次数
     */
    @Select("SELECT COUNT(*) FROM download_statistics WHERE DATE(download_time) = CURDATE() AND download_status = 1")
    Long getTodayDownloads();

    /**
     * 获取本月下载次数
     *
     * @return 本月下载次数
     */
    @Select("SELECT COUNT(*) FROM download_statistics WHERE YEAR(download_time) = YEAR(NOW()) AND MONTH(download_time) = MONTH(NOW()) AND download_status = 1")
    Long getThisMonthDownloads();

    /**
     * 获取独立IP数量
     *
     * @return 独立IP数量
     */
    @Select("SELECT COUNT(DISTINCT ip_address) FROM download_statistics WHERE download_status = 1")
    Long getUniqueIpCount();

    /**
     * 获取最近7天的下载统计
     *
     * @return 最近7天的下载统计
     */
    @Select("SELECT DATE(download_time) as date, COUNT(*) as count " +
            "FROM download_statistics " +
            "WHERE download_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND download_status = 1 " +
            "GROUP BY DATE(download_time) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> getRecentSevenDaysStatistics();

    /**
     * 获取最近30天的下载统计
     *
     * @return 最近30天的下载统计
     */
    @Select("SELECT DATE(download_time) as date, COUNT(*) as count " +
            "FROM download_statistics " +
            "WHERE download_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND download_status = 1 " +
            "GROUP BY DATE(download_time) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> getRecentThirtyDaysStatistics();

    /**
     * 获取指定日期范围的下载统计
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 下载统计
     */
    @Select("SELECT DATE(download_time) as date, COUNT(*) as count " +
            "FROM download_statistics " +
            "WHERE DATE(download_time) BETWEEN #{startDate} AND #{endDate} AND download_status = 1 " +
            "GROUP BY DATE(download_time) " +
            "ORDER BY date DESC")
    List<Map<String, Object>> getStatisticsByDateRange(@Param("startDate") LocalDate startDate, 
                                                       @Param("endDate") LocalDate endDate);
}