package com.zibbava.edgemind.server.dto;

import lombok.Data;

/**
 * 许可证记录统计DTO
 */
@Data
public class LicenseRecordStatisticsDTO {

    /**
     * 总许可证数量
     */
    private Long totalCount;

    /**
     * 免费试用许可证数量
     */
    private Long freeTrialCount;

    /**
     * 管理员生成许可证数量
     */
    private Long adminGeneratedCount;

    /**
     * 已激活许可证数量
     */
    private Long activeCount;

    /**
     * 未激活许可证数量
     */
    private Long inactiveCount;

    /**
     * 已过期许可证数量
     */
    private Long expiredCount;

    /**
     * 已禁用许可证数量
     */
    private Long disabledCount;

    /**
     * 即将过期许可证数量（7天内）
     */
    private Long expiringSoonCount;

    /**
     * 今日新增许可证数量
     */
    private Long todayNewCount;

    /**
     * 本月新增许可证数量
     */
    private Long thisMonthNewCount;
}