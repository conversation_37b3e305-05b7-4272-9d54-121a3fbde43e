package com.zibbava.edgemind.server.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.server.common.Result;
import com.zibbava.edgemind.server.dto.ChangePasswordDTO;
import com.zibbava.edgemind.server.dto.DownloadStatisticsDTO;
import com.zibbava.edgemind.server.dto.LoginRequestDTO;
import com.zibbava.edgemind.server.entity.AdminUser;
import com.zibbava.edgemind.server.service.AdminUserService;
import com.zibbava.edgemind.server.service.DownloadStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;

/**
 * 管理后台控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Controller
@RequestMapping("/admin")
@RequiredArgsConstructor
@Validated
public class AdminController {

    private final AdminUserService adminUserService;
    private final DownloadStatisticsService downloadStatisticsService;

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage() {
        // 如果已经登录，直接跳转到主页
        if (StpUtil.isLogin()) {
            return "redirect:/admin/main";
        }
        return "admin/login";
    }

    /**
     * 管理后台主页 - 新的整合页面
     */
    @GetMapping("/main")
    @SaCheckLogin
    public String main(Model model) {
        try {
            // 获取当前登录用户信息
            AdminUser currentAdmin = adminUserService.getCurrentAdmin();
            model.addAttribute("currentAdmin", currentAdmin);
            
            // 获取下载统计数据
            DownloadStatisticsDTO statistics = downloadStatisticsService.getStatisticsOverview();
            model.addAttribute("statistics", statistics);
            
            return "admin/main";
        } catch (Exception e) {
            log.error("获取管理后台数据失败", e);
            model.addAttribute("error", "获取数据失败: " + e.getMessage());
            return "admin/main";
        }
    }

    /**
     * 管理后台主页 - 重定向到新的整合页面
     */
    @GetMapping("/dashboard")
    @SaCheckLogin
    public String dashboard() {
        return "redirect:/admin/main";
    }

    /**
     * 修改密码页面
     */
    @GetMapping("/change-password")
    @SaCheckLogin
    public String changePasswordPage(Model model) {
        AdminUser currentAdmin = adminUserService.getCurrentAdmin();
        model.addAttribute("currentAdmin", currentAdmin);
        return "admin/change-password";
    }

    /**
     * 执行登录
     */
    @PostMapping("/login")
    @ResponseBody
    public Result<String> doLogin(@Valid @RequestBody LoginRequestDTO loginRequest, HttpServletRequest request) {
        try {
            // 验证验证码
            if (!verifyCaptcha(loginRequest.getCaptcha(), request)) {
                return Result.error("验证码错误或已过期");
            }
            
            String clientIp = downloadStatisticsService.getClientIpAddress(request);
            String token = adminUserService.login(loginRequest, clientIp);
            log.info("管理员 {} 登录成功，IP: {}", loginRequest.getUsername(), clientIp);
            return Result.success("登录成功", token);
        } catch (Exception e) {
            log.warn("管理员登录失败: {}, IP: {}", e.getMessage(), downloadStatisticsService.getClientIpAddress(request));
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 验证验证码
     */
    private boolean verifyCaptcha(String captcha, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String sessionCaptcha = (String) session.getAttribute("captcha_code");
            
            if (sessionCaptcha == null || captcha == null) {
                return false;
            }
            
            boolean isValid = sessionCaptcha.equalsIgnoreCase(captcha.trim());
            
            if (isValid) {
                // 验证成功后清除session中的验证码
                session.removeAttribute("captcha_code");
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("验证码验证失败", e);
            return false;
        }
    }

    /**
     * 登出
     */
    @PostMapping("/logout")
    @ResponseBody
    public Result<Void> logout() {
        try {
            adminUserService.logout();
            return Result.success();
        } catch (Exception e) {
            log.error("登出失败", e);
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/current-user")
    @ResponseBody
    @SaCheckLogin
    public Result<AdminUser> getCurrentUser() {
        try {
            AdminUser currentAdmin = adminUserService.getCurrentAdmin();
            return Result.success(currentAdmin);
        } catch (Exception e) {
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    @ResponseBody
    @SaCheckLogin
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordDTO changePasswordDTO) {
        try {
            boolean success = adminUserService.changePassword(changePasswordDTO);
            if (success) {
                return Result.success();
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败: " + e.getMessage());
        }
    }

    /**
     * 获取下载统计数据
     */
    @GetMapping("/statistics")
    @ResponseBody
    @SaCheckLogin
    public Result<DownloadStatisticsDTO> getStatistics() {
        try {
            DownloadStatisticsDTO statistics = downloadStatisticsService.getStatisticsOverview();
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取下载统计失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 许可证管理页面
     */
    @GetMapping("/license-management")
    @SaCheckLogin
    public String licenseManagement() {
        return "license-management";
    }
}