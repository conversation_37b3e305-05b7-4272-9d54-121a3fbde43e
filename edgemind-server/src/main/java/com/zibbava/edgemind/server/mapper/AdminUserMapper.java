package com.zibbava.edgemind.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.server.entity.AdminUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;

/**
 * 管理员用户Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {

    /**
     * 根据用户名查询管理员用户
     *
     * @param username 用户名
     * @return 管理员用户
     */
    @Select("SELECT * FROM admin_user WHERE username = #{username} AND status = 1")
    AdminUser selectByUsername(@Param("username") String username);

    /**
     * 更新最后登录信息
     *
     * @param id 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @return 更新行数
     */
    @Update("UPDATE admin_user SET last_login_time = #{loginTime}, last_login_ip = #{loginIp} WHERE id = #{id}")
    int updateLastLoginInfo(@Param("id") Long id, 
                           @Param("loginTime") LocalDateTime loginTime, 
                           @Param("loginIp") String loginIp);

    /**
     * 更新密码
     *
     * @param id 用户ID
     * @param password 新密码(加密后)
     * @param updateBy 更新人
     * @return 更新行数
     */
    @Update("UPDATE admin_user SET password = #{password}, update_by = #{updateBy}, update_time = NOW() WHERE id = #{id}")
    int updatePassword(@Param("id") Long id, 
                      @Param("password") String password, 
                      @Param("updateBy") String updateBy);
}