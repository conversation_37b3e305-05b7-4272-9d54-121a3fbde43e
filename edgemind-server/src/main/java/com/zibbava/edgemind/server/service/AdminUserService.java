package com.zibbava.edgemind.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.server.dto.ChangePasswordDTO;
import com.zibbava.edgemind.server.dto.LoginRequestDTO;
import com.zibbava.edgemind.server.entity.AdminUser;

/**
 * 管理员用户服务接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface AdminUserService extends IService<AdminUser> {

    /**
     * 管理员登录
     *
     * @param loginRequest 登录请求
     * @param clientIp 客户端IP
     * @return 登录token
     */
    String login(LoginRequestDTO loginRequest, String clientIp);

    /**
     * 管理员登出
     */
    void logout();

    /**
     * 获取当前登录的管理员信息
     *
     * @return 管理员信息
     */
    AdminUser getCurrentAdmin();

    /**
     * 修改密码
     *
     * @param changePasswordDTO 修改密码请求
     * @return 是否成功
     */
    boolean changePassword(ChangePasswordDTO changePasswordDTO);

    /**
     * 根据用户名查询管理员
     *
     * @param username 用户名
     * @return 管理员信息
     */
    AdminUser getByUsername(String username);

    /**
     * 更新最后登录信息
     *
     * @param adminId 管理员ID
     * @param clientIp 客户端IP
     */
    void updateLastLoginInfo(Long adminId, String clientIp);
}