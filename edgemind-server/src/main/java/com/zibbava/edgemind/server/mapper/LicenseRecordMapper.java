package com.zibbava.edgemind.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.server.entity.LicenseRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 许可证记录Mapper接口
 */
@Mapper
public interface LicenseRecordMapper extends BaseMapper<LicenseRecord> {

    /**
     * 分页查询许可证记录
     *
     * @param page 分页参数
     * @param licenseType 许可证类型
     * @param status 状态
     * @param hardwareFingerprint 硬件指纹
     * @param systemIdentifier 系统标识符
     * @param generatedBy 生成者
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<LicenseRecord> selectLicenseRecordPage(
            Page<LicenseRecord> page,
            @Param("licenseType") Integer licenseType,
            @Param("status") Integer status,
            @Param("hardwareFingerprint") String hardwareFingerprint,
            @Param("systemIdentifier") String systemIdentifier,
            @Param("generatedBy") String generatedBy,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计许可证数量
     *
     * @param licenseType 许可证类型
     * @param status 状态
     * @return 数量
     */
    Long countByTypeAndStatus(@Param("licenseType") Integer licenseType, @Param("status") Integer status);

    /**
     * 查询即将过期的许可证
     *
     * @param days 天数
     * @return 许可证列表
     */
    List<LicenseRecord> selectExpiringLicenses(@Param("days") Integer days);

    // 已移除过期状态更新方法 - 仅保留记录查询功能
}