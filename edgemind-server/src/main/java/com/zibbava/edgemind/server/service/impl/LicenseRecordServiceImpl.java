package com.zibbava.edgemind.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.server.dto.*;
import com.zibbava.edgemind.server.entity.LicenseRecord;
import com.zibbava.edgemind.server.mapper.LicenseRecordMapper;
import com.zibbava.edgemind.server.service.LicenseRecordService;
import com.zibbava.edgemind.server.utils.LicenseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 许可证记录服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LicenseRecordServiceImpl implements LicenseRecordService {

    private final LicenseRecordMapper licenseRecordMapper;

    @Override
    @Transactional
    public String generateAdminLicense(LicenseInfoDTO licenseInfo, String generatedBy, String clientInfo) {
        if (licenseInfo == null ||
                !StringUtils.hasText(licenseInfo.getHardwareFingerprint()) ||
                !StringUtils.hasText(licenseInfo.getSystemIdentifier())) {
            log.warn("管理员许可证生成参数不完整");
            return null;
        }

        try {
            // 生成许可证密钥
            String licenseKey = LicenseUtils.generateLicenseKey(
                    licenseInfo.getHardwareFingerprint(),
                    licenseInfo.getSystemIdentifier(),
                    licenseInfo.getExpireTime()
            );

            if (licenseKey == null) {
                log.error("生成管理员许可证失败");
                return null;
            }

            // 保存许可证记录
            LicenseRecord record = LicenseRecord.builder()
                    .licenseKey(licenseKey)
                    .hardwareFingerprint(licenseInfo.getHardwareFingerprint())
                    .systemIdentifier(licenseInfo.getSystemIdentifier())
                    .licenseType(LicenseRecord.LicenseType.ADMIN_GENERATED.getCode())
                    .status(LicenseRecord.LicenseStatus.ACTIVE.getCode())
                    .createTime(LocalDateTime.now())
                    .expireTime(licenseInfo.getExpireTime())
                    .generatedBy(generatedBy)
                    .clientInfo(clientInfo)
                    .remark("管理员生成的许可证（默认激活状态）")
                    .build();

            licenseRecordMapper.insert(record);
            log.info("管理员 {} 为硬件指纹 {} 生成许可证成功", generatedBy, licenseInfo.getHardwareFingerprint());
            return licenseKey;
        } catch (Exception e) {
            log.error("生成管理员许可证时发生异常", e);
            return null;
        }
    }

    @Override
    @Transactional
    public String generateFreeTrialLicense(FreeTrialRequestDTO request, String clientInfo) {
        if (request == null ||
                !StringUtils.hasText(request.getHardwareFingerprint()) ||
                !StringUtils.hasText(request.getSystemIdentifier())) {
            log.warn("免费试用许可证生成参数不完整");
            return null;
        }

        // 检查该硬件指纹是否已经使用过免费额度
        if (hasUsedFreeTrial(request.getHardwareFingerprint())) {
            log.warn("硬件指纹 {} 已使用过免费试用额度", request.getHardwareFingerprint());
            return null;
        }

        try {
            // 生成一个月的免费试用许可证
            LocalDateTime expireTime = LocalDateTime.now().plusMonths(1);
            String licenseKey = LicenseUtils.generateLicenseKey(
                    request.getHardwareFingerprint(),
                    request.getSystemIdentifier(),
                    expireTime
            );

            if (licenseKey == null) {
                log.error("生成免费试用许可证失败");
                return null;
            }

            // 保存许可证记录
            LicenseRecord record = LicenseRecord.builder()
                    .licenseKey(licenseKey)
                    .hardwareFingerprint(request.getHardwareFingerprint())
                    .systemIdentifier(request.getSystemIdentifier())
                    .licenseType(LicenseRecord.LicenseType.FREE_TRIAL.getCode())
                    .status(LicenseRecord.LicenseStatus.ACTIVE.getCode())
                    .createTime(LocalDateTime.now())
                    .expireTime(expireTime)
                    .generatedBy("SYSTEM")
                    .clientInfo(clientInfo)
                    .remark("自动生成的免费试用许可证 - " + (request.getRemark() != null ? request.getRemark() : ""))
                    .build();

            licenseRecordMapper.insert(record);
            log.info("为硬件指纹 {} 生成免费试用许可证成功", request.getHardwareFingerprint());
            return licenseKey;
        } catch (Exception e) {
            log.error("生成免费试用许可证时发生异常", e);
            return null;
        }
    }

    @Override
    public IPage<LicenseRecordDTO> getLicenseRecordPage(LicenseRecordQueryDTO queryDTO) {
        Page<LicenseRecord> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        IPage<LicenseRecord> recordPage = licenseRecordMapper.selectLicenseRecordPage(
                page,
                queryDTO.getLicenseType(),
                queryDTO.getStatus(),
                queryDTO.getHardwareFingerprint(),
                queryDTO.getSystemIdentifier(),
                queryDTO.getGeneratedBy(),
                queryDTO.getStartTime(),
                queryDTO.getEndTime()
        );

        // 转换为DTO
        IPage<LicenseRecordDTO> dtoPage = recordPage.convert(this::convertToDTO);
        return dtoPage;
    }

    @Override
    public LicenseRecordDTO getLicenseRecordById(Long id) {
        LicenseRecord record = licenseRecordMapper.selectById(id);
        return record != null ? convertToDTO(record) : null;
    }

    @Override
    public LicenseRecordDTO getLicenseRecordByKey(String licenseKey) {
        LambdaQueryWrapper<LicenseRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicenseRecord::getLicenseKey, licenseKey);
        LicenseRecord record = licenseRecordMapper.selectOne(queryWrapper);
        return record != null ? convertToDTO(record) : null;
    }

    // 已移除状态更新方法实现 - 本地客户端自行维护状态

    // 已移除激活方法实现 - 本地客户端自行处理激活

    // 已移除禁用方法实现 - 本地客户端自行处理状态

    @Override
    @Transactional
    public boolean deleteLicenseRecord(Long id) {
        return licenseRecordMapper.deleteById(id) > 0;
    }

    @Override
    public LicenseRecordStatisticsDTO getLicenseStatistics() {
        LicenseRecordStatisticsDTO statistics = new LicenseRecordStatisticsDTO();
        
        // 总数
        statistics.setTotalCount(licenseRecordMapper.countByTypeAndStatus(null, null));
        
        // 按类型统计
        statistics.setFreeTrialCount(licenseRecordMapper.countByTypeAndStatus(
                LicenseRecord.LicenseType.FREE_TRIAL.getCode(), null));
        statistics.setAdminGeneratedCount(licenseRecordMapper.countByTypeAndStatus(
                LicenseRecord.LicenseType.ADMIN_GENERATED.getCode(), null));
        
        // 按状态统计
        statistics.setActiveCount(licenseRecordMapper.countByTypeAndStatus(
                null, LicenseRecord.LicenseStatus.ACTIVE.getCode()));
        statistics.setInactiveCount(licenseRecordMapper.countByTypeAndStatus(
                null, LicenseRecord.LicenseStatus.INACTIVE.getCode()));
        statistics.setExpiredCount(licenseRecordMapper.countByTypeAndStatus(
                null, LicenseRecord.LicenseStatus.EXPIRED.getCode()));
        statistics.setDisabledCount(licenseRecordMapper.countByTypeAndStatus(
                null, LicenseRecord.LicenseStatus.DISABLED.getCode()));
        
        // 即将过期数量
        List<LicenseRecord> expiringLicenses = licenseRecordMapper.selectExpiringLicenses(7);
        statistics.setExpiringSoonCount((long) expiringLicenses.size());
        
        // 今日和本月新增（这里简化实现，实际可能需要更复杂的查询）
        LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        
        // 这里需要额外的查询方法，暂时设为0
        statistics.setTodayNewCount(0L);
        statistics.setThisMonthNewCount(0L);
        
        return statistics;
    }

    @Override
    public List<LicenseRecordDTO> getExpiringLicenses(Integer days) {
        List<LicenseRecord> records = licenseRecordMapper.selectExpiringLicenses(days);
        return records.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    // 已移除过期状态更新方法实现 - 仅保留记录查询功能

    @Override
    public boolean hasUsedFreeTrial(String hardwareFingerprint) {
        LambdaQueryWrapper<LicenseRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicenseRecord::getHardwareFingerprint, hardwareFingerprint)
                   .eq(LicenseRecord::getLicenseType, LicenseRecord.LicenseType.FREE_TRIAL.getCode());
        
        return licenseRecordMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean isLicenseValid(String licenseKey) {
        LambdaQueryWrapper<LicenseRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LicenseRecord::getLicenseKey, licenseKey)
                   .eq(LicenseRecord::getStatus, LicenseRecord.LicenseStatus.ACTIVE.getCode());
        
        LicenseRecord record = licenseRecordMapper.selectOne(queryWrapper);
        if (record == null) {
            return false;
        }
        
        // 检查是否过期（不自动更新状态，只返回验证结果）
        if (record.getExpireTime() != null && record.getExpireTime().isBefore(LocalDateTime.now())) {
            return false;
        }
        
        return true;
    }

    /**
     * 转换实体为DTO
     */
    private LicenseRecordDTO convertToDTO(LicenseRecord record) {
        LicenseRecordDTO dto = new LicenseRecordDTO();
        BeanUtils.copyProperties(record, dto);
        
        // 设置类型和状态描述
        LicenseRecord.LicenseType licenseType = LicenseRecord.LicenseType.fromCode(record.getLicenseType());
        if (licenseType != null) {
            dto.setLicenseTypeDesc(licenseType.getDescription());
        }
        
        LicenseRecord.LicenseStatus licenseStatus = LicenseRecord.LicenseStatus.fromCode(record.getStatus());
        if (licenseStatus != null) {
            dto.setStatusDesc(licenseStatus.getDescription());
        }
        
        // 计算是否即将过期和剩余天数
        if (record.getExpireTime() != null) {
            LocalDateTime now = LocalDateTime.now();
            if (record.getExpireTime().isAfter(now)) {
                // 计算剩余天数，向上取整确保当天内也显示至少1天
                long remainingDays = ChronoUnit.DAYS.between(now.toLocalDate(), record.getExpireTime().toLocalDate());
                if (remainingDays == 0 && record.getExpireTime().isAfter(now)) {
                    remainingDays = 1; // 当天内但未过期，显示1天
                }
                dto.setRemainingDays(remainingDays);
                dto.setExpiringSoon(remainingDays <= 7);
            } else {
                dto.setRemainingDays(0L);
                dto.setExpiringSoon(false);
            }
        } else {
            dto.setRemainingDays(-1L); // 永不过期
            dto.setExpiringSoon(false);
        }
        
        return dto;
    }
}