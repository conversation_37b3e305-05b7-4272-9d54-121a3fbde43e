package com.zibbava.edgemind.server.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.server.dto.*;
import com.zibbava.edgemind.server.dto.FreeTrialRequestDTO;
import com.zibbava.edgemind.server.entity.LicenseRecord;

import java.util.List;

/**
 * 许可证记录服务接口
 */
public interface LicenseRecordService {

    /**
     * 生成管理员许可证并记录
     *
     * @param licenseInfo 许可证信息
     * @param generatedBy 生成者
     * @param clientInfo 客户端信息
     * @return 生成的许可证密钥
     */
    String generateAdminLicense(LicenseInfoDTO licenseInfo, String generatedBy, String clientInfo);


    /**
     * 分页查询许可证记录
     *
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    IPage<LicenseRecordDTO> getLicenseRecordPage(LicenseRecordQueryDTO queryDTO);

    /**
     * 根据ID获取许可证记录详情
     *
     * @param id 许可证记录ID
     * @return 许可证记录详情
     */
    LicenseRecordDTO getLicenseRecordById(Long id);

    /**
     * 根据许可证密钥获取记录
     *
     * @param licenseKey 许可证密钥
     * @return 许可证记录
     */
    LicenseRecordDTO getLicenseRecordByKey(String licenseKey);

    // 已移除状态更新方法 - 本地客户端自行维护状态

    // 已移除激活方法 - 本地客户端自行处理激活

    // 已移除禁用方法 - 本地客户端自行处理状态

    /**
     * 删除许可证记录
     *
     * @param id 许可证记录ID
     * @return 是否成功
     */
    boolean deleteLicenseRecord(Long id);

    /**
     * 获取许可证统计信息
     *
     * @return 统计信息
     */
    LicenseRecordStatisticsDTO getLicenseStatistics();

    /**
     * 生成免费试用许可证
     *
     * @param request 免费试用请求
     * @param clientInfo 客户端信息
     * @return 生成的许可证密钥，如果该硬件指纹已使用过免费额度则返回null
     */
    String generateFreeTrialLicense(FreeTrialRequestDTO request, String clientInfo);

    /**
     * 检查硬件指纹是否已使用过免费试用
     *
     * @param hardwareFingerprint 硬件指纹
     * @return 是否已使用过免费试用
     */
    boolean hasUsedFreeTrial(String hardwareFingerprint);

    /**
     * 获取即将过期的许可证列表
     *
     * @param days 天数
     * @return 许可证列表
     */
    List<LicenseRecordDTO> getExpiringLicenses(Integer days);


    /**
     * 验证许可证是否有效
     *
     * @param licenseKey 许可证密钥
     * @return 是否有效
     */
    boolean isLicenseValid(String licenseKey);
}